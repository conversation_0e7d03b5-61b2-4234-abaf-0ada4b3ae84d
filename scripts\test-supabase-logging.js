import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://vgjhxvpxooboinitmwrg.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.-hsT_nvM2By1bDV5H03KvXNxEERbuzSK3-E2QMi9cT0';

console.log('🧪 Testing Supabase AI Logging System...\n');

async function testSupabaseConnection() {
    console.log('1️⃣ Testing Supabase connection...');
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    
    try {
        // Test basic connection with REST API health check
        const healthCheck = await fetch(`${SUPABASE_URL}/rest/v1/`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
                'apikey': SUPABASE_SERVICE_ROLE_KEY
            }
        });
        
        if (!healthCheck.ok) {
            console.error('❌ Connection test failed:', healthCheck.status, healthCheck.statusText);
            return false;
        }
        
        console.log('✅ Connection successful');
        console.log('📋 API Status:', healthCheck.status);
        return true;
    } catch (err) {
        console.error('❌ Connection error:', err);
        return false;
    }
}

async function testTableAccess() {
    console.log('\n2️⃣ Testing table access...');
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    
    try {
        // Test access to ai_analysis_logs table
        const { data, error } = await supabase
            .from('ai_analysis_logs')
            .select('id')
            .limit(1);
        
        if (error) {
            console.error('❌ Table access failed:', error);
            return false;
        }
        
        console.log('✅ Table access successful');
        return true;
    } catch (err) {
        console.error('❌ Table access error:', err);
        return false;
    }
}

async function testInsertLog() {
    console.log('\n3️⃣ Testing log insertion...');
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    
    const testLogData = {
        session_id: `test_session_${Date.now()}`,
        form_url: 'https://test-form.example.com',
        extension_version: '1.0.0',
        model_name: 'test-model',
        user_agent: 'Test/1.0',
        form_data: { test_field: 'test_value' },
        filled_fields_count: 1,
        trigger_reason: 'test_run',
        previous_recommendations: [],
        dismissed_recommendations: [],
        full_prompt: [{ role: 'user', content: 'test prompt' }],
        model_parameters: { temperature: 0.1 },
        tool_calls: [],
        iterations: 1,
        raw_ai_response: { test: 'response' },
        total_duration_ms: 1000,
        api_response_time_ms: 500,
        prompt_tokens: 100,
        completion_tokens: 50,
        suggestions: [],
        overall_assessment: 'Test assessment',
        success: true,
        errors: []
    };
    
    try {
        // Insert test log data
        const { data, error } = await supabase
            .from('ai_analysis_logs')
            .insert(testLogData)
            .select('id');
        
        if (error) {
            console.error('❌ Insert failed:', error);
            return false;
        }
        
        console.log('✅ Insert successful:', data);
        return data[0]?.id;
    } catch (err) {
        console.error('❌ Insert error:', err);
        return false;
    }
}

async function testQueryLogs() {
    console.log('\n4️⃣ Testing log querying...');
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    
    try {
        // Try to query recent logs
        const { data, error } = await supabase
            .from('ai_analysis_logs')
            .select('id, session_id, form_url, success, created_at')
            .order('created_at', { ascending: false })
            .limit(5);
        
        if (error) {
            console.error('❌ Query failed:', error);
            return false;
        }
        
        console.log('✅ Query successful');
        console.log('📊 Recent logs:', data);
        return true;
    } catch (err) {
        console.error('❌ Query error:', err);
        return false;
    }
}

async function testLoggingService() {
    console.log('\n5️⃣ Testing logging service integration...');
    
    // Import our logging service
    try {
        const { aiLogger } = await import('../utils/aiLogger.js');
        
        const testData = {
            formUrl: 'https://test-integration.example.com',
            modelName: 'test-model-integration',
            formData: { integration_test: 'value' },
            filledFieldsCount: 2,
            triggerReason: 'integration_test',
            previousRecommendations: [],
            dismissedRecommendations: [],
            fullPrompt: [{ role: 'user', content: 'integration test' }],
            modelParameters: { temperature: 0.2 },
            toolCalls: [],
            iterations: 1,
            rawAIResponse: { integration: 'test' },
            totalDuration: 2000,
            apiResponseTime: 800,
            promptTokens: 150,
            completionTokens: 75,
            suggestions: [{ id: 'test-1', field_id: 'test', suggestion: 'Test suggestion', priority: 'medium', severity: 'low', type: 'missing_info' }],
            overallAssessment: 'Integration test assessment',
            success: true
        };
        
        const logId = await aiLogger.logAIAnalysis(testData);
        
        if (logId) {
            console.log('✅ Logging service test successful, ID:', logId);
            return true;
        } else {
            console.error('❌ Logging service test failed');
            return false;
        }
    } catch (err) {
        console.error('❌ Logging service error:', err);
        return false;
    }
}

async function runAllTests() {
    console.log('🚀 Starting comprehensive Supabase logging tests...\n');
    
    const results = {
        connection: false,
        tableAccess: false,
        insert: false,
        query: false,
        service: false
    };
    
    // Run tests sequentially
    results.connection = await testSupabaseConnection();
    
    if (results.connection) {
        results.tableAccess = await testTableAccess();
        
        if (results.tableAccess) {
            results.insert = await testInsertLog();
            results.query = await testQueryLogs();
            results.service = await testLoggingService();
        }
    }
    
    // Summary
    console.log('\n📋 TEST SUMMARY:');
    console.log('================');
    console.log(`Connection: ${results.connection ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Table Access: ${results.tableAccess ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Insert Test: ${results.insert ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Query Test: ${results.query ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Service Test: ${results.service ? '✅ PASS' : '❌ FAIL'}`);
    
    const passCount = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n🎯 Overall: ${passCount}/${totalTests} tests passed`);
    
    if (passCount === totalTests) {
        console.log('🎉 All tests passed! Logging system is ready.');
    } else {
        console.log('⚠️  Some tests failed. Check errors above for troubleshooting.');
        
        // Troubleshooting tips
        console.log('\n🔧 TROUBLESHOOTING TIPS:');
        if (!results.connection) {
            console.log('- Check Supabase URL and service role key');
            console.log('- Verify project is active and accessible');
        }
        if (!results.tableAccess) {
            console.log('- Run the schema creation SQL in Supabase SQL Editor');
            console.log('- Check if diamond-legal schema exists');
        }
        if (!results.insert) {
            console.log('- Verify table permissions for service role');
            console.log('- Check table structure matches expected schema');
        }
    }
}

// Run tests
runAllTests().catch(console.error);