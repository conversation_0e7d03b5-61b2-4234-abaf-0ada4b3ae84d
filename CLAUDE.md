# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start development server with Chrome
- `npm run dev:firefox` - Start development server with Firefox
- `npm run build` - Build extension for production
- `npm run build:firefox` - Build extension for Firefox
- `npm run zip` - Create distributable zip for Chrome
- `npm run zip:firefox` - Create distributable zip for Firefox
- `npm run compile` - Type check without building
- `npx vitest` - Run tests
- `npx vitest run` - Run tests once
- `npx vitest utils/ai.integration.test.ts` - Run specific test file

## Architecture Overview

**Chrome Extension using WXT Framework**: AI-powered legal form analysis tool that monitors Lawmatics forms and provides intelligent recommendations via sidebar and toast notifications.

### Key Entry Points

1. **Background Script** (`entrypoints/background.ts`) - Manages sidebar state and inter-component messaging
2. **Content Script** (`entrypoints/content/index.tsx`) - Injected into `https://app.lawmatics.com/forms/*`, monitors form changes
3. **Sidepanel** (`entrypoints/sidepanel.html` + `sidepanel-main.tsx`) - Displays AI recommendations

### Core Components

- **FormWatcher** (`components/FormWatcher.tsx`) - Monitors form inputs using MutationObserver, triggers AI analysis after 6+ fields filled
- **SidebarApp** (`components/SidebarApp.tsx`) - Displays AI suggestions with priority-based styling
- **Toast System** - Notifies users of high-priority recommendations when sidebar is closed

### AI Integration

Located in `utils/ai.ts` - Uses OpenRouter API with Google Gemini 2.5 Flash model to analyze legal forms against comprehensive quality rules. Returns structured JSON with suggestions, priorities, and assessments.

### State Management

- Chrome storage API for cross-component data sharing
- Background script tracks sidebar open/close state per tab
- Real-time updates via storage listeners and runtime messaging

### Testing

Uses Vitest with WXT integration. Integration tests in `utils/ai.integration.test.ts` verify AI service functionality with real API calls (30s timeout).

## Technical Notes

- **Target Platform**: Chrome Extension (Manifest V3)
- **Framework**: WXT (Web Extension Toolkit) + React 19 + TypeScript
- **AI Provider**: OpenRouter with Google Gemini 2.5 Flash
- **Form Monitoring**: MutationObserver with throttling (5s initial, 30s subsequent)
- **UI Isolation**: Shadow DOM prevents CSS conflicts with host pages
- **Field Threshold**: AI analysis triggers after 6+ form fields are filled
