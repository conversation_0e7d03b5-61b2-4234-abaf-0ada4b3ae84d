-- Fix permissions for diamond-legal schema
-- Run this in your Supabase SQL Editor

-- Grant schema usage to all roles
GRANT USAGE ON SCHEMA "diamond-legal" TO anon, authenticated, service_role;

-- Grant table permissions
GRANT ALL ON ALL TABLES IN SCHEMA "diamond-legal" TO anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA "diamond-legal" TO anon, authenticated, service_role;
GRANT ALL ON ALL ROUTINES IN SCHEMA "diamond-legal" TO anon, authenticated, service_role;

-- Set default privileges for future objects in diamond-legal schema
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA "diamond-legal" 
    GRANT ALL ON TABLES TO anon, authenticated, service_role;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA "diamond-legal" 
    GRANT ALL ON SEQUENCES TO anon, authenticated, service_role;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA "diamond-legal" 
    GRANT ALL ON ROUTINES TO anon, authenticated, service_role;

-- Verify permissions were granted
SELECT 
    schemaname,
    tablename,
    tableowner,
    has_table_privilege('service_role', schemaname||'.'||tablename, 'SELECT') as can_select,
    has_table_privilege('service_role', schemaname||'.'||tablename, 'INSERT') as can_insert,
    has_table_privilege('service_role', schemaname||'.'||tablename, 'UPDATE') as can_update,
    has_table_privilege('service_role', schemaname||'.'||tablename, 'DELETE') as can_delete
FROM pg_tables 
WHERE schemaname = 'diamond-legal'
ORDER BY tablename;