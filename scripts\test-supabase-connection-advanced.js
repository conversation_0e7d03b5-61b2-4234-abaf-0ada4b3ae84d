import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://vgjhxvpxooboinitmwrg.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZnamh4dnB4b29ib2luaXRtd3JnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcxNTI3Njc3NSwiZXhwIjoyMDMwODUyNzc1fQ.-hsT_nvM2By1bDV5H03KvXNxEERbuzSK3-E2QMi9cT0';

console.log('🔍 Advanced Supabase Connection and Schema Diagnostics\n');

async function discoverTablesAndSchemas() {
    console.log('1️⃣ Discovering available tables and schemas...');
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    
    try {
        // Use RPC to discover schemas and tables
        const { data, error } = await supabase.rpc('exec_sql', {
            sql: `
                SELECT 
                    schemaname,
                    tablename,
                    tableowner,
                    hasindexes,
                    hasrules,
                    hastriggers
                FROM pg_tables 
                WHERE schemaname NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
                ORDER BY schemaname, tablename;
            `
        });
        
        if (error) {
            console.error('❌ Schema discovery via RPC failed:', error);
            console.log('🔍 Trying alternative approach...');
            
            // Fallback: try to get schema info via direct query
            const { data: data2, error: error2 } = await supabase.rpc('get_schema_info');
            if (error2) {
                console.error('❌ Alternative schema discovery failed:', error2);
                return false;
            } else {
                console.log('✅ Schema info retrieved via alternative method');
                console.log('📋 Schema data:', data2);
                return true;
            }
        }
        
        console.log('✅ Schema discovery successful');
        console.log('📋 Available tables:');
        data.forEach(table => {
            console.log(`   ${table.schemaname}.${table.tablename} (owner: ${table.tableowner})`);
        });
        
        // Look for our specific tables
        const aiTables = data.filter(table => 
            table.tablename.includes('ai_analysis') || 
            table.tablename.includes('user_interactions') ||
            table.tablename.includes('form_metadata')
        );
        
        if (aiTables.length > 0) {
            console.log('\n🎯 Found AI logging tables:');
            aiTables.forEach(table => {
                console.log(`   ✅ ${table.schemaname}.${table.tablename}`);
            });
        } else {
            console.log('\n⚠️  No AI logging tables found');
        }
        
        return data;
    } catch (err) {
        console.error('❌ Discovery error:', err);
        return false;
    }
}

async function testSchemaAccess() {
    console.log('\n2️⃣ Testing schema access methods...');
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    const schemas = ['public', 'diamond-legal'];
    const tables = ['ai_analysis_logs', 'user_interactions', 'form_metadata'];
    
    const results = {};
    
    for (const schema of schemas) {
        results[schema] = {};
        console.log(`\n🔍 Testing schema: ${schema}`);
        
        for (const table of tables) {
            try {
                // Method 1: Direct schema.from() access
                const { data, error } = await supabase
                    .schema(schema)
                    .from(table)
                    .select('*')
                    .limit(1);
                
                if (error) {
                    console.log(`   ❌ ${schema}.${table} - Error: ${error.message}`);
                    results[schema][table] = { method: 'schema.from', success: false, error: error.message };
                } else {
                    console.log(`   ✅ ${schema}.${table} - Accessible (${data.length} rows)`);
                    results[schema][table] = { method: 'schema.from', success: true, rowCount: data.length };
                }
            } catch (err) {
                console.log(`   ❌ ${schema}.${table} - Exception: ${err.message}`);
                results[schema][table] = { method: 'schema.from', success: false, error: err.message };
            }
        }
    }
    
    return results;
}

async function testDirectTableAccess() {
    console.log('\n3️⃣ Testing direct table access without schema prefix...');
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    const tables = ['ai_analysis_logs', 'user_interactions', 'form_metadata'];
    
    const results = {};
    
    for (const table of tables) {
        try {
            const { data, error } = await supabase
                .from(table)
                .select('*')
                .limit(1);
            
            if (error) {
                console.log(`   ❌ ${table} - Error: ${error.message}`);
                results[table] = { success: false, error: error.message };
            } else {
                console.log(`   ✅ ${table} - Accessible (${data.length} rows)`);
                results[table] = { success: true, rowCount: data.length };
            }
        } catch (err) {
            console.log(`   ❌ ${table} - Exception: ${err.message}`);
            results[table] = { success: false, error: err.message };
        }
    }
    
    return results;
}

async function testInsertOperation() {
    console.log('\n4️⃣ Testing insert operation...');
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    
    const testData = {
        session_id: `test_${Date.now()}`,
        form_url: 'https://test.example.com',
        model_name: 'test-model',
        form_data: { test: 'data' },
        filled_fields_count: 1,
        trigger_reason: 'test',
        previous_recommendations: [],
        dismissed_recommendations: [],
        full_prompt: [{ role: 'user', content: 'test' }],
        model_parameters: { temperature: 0.1 },
        tool_calls: [],
        iterations: 1,
        total_duration_ms: 1000,
        api_response_time_ms: 500,
        suggestions: [],
        overall_assessment: 'Test',
        success: true,
        errors: []
    };
    
    // Try different approaches
    const approaches = [
        { name: 'Direct table access', method: async () => supabase.from('ai_analysis_logs').insert(testData).select('id') },
        { name: 'Public schema', method: async () => supabase.schema('public').from('ai_analysis_logs').insert(testData).select('id') },
        { name: 'Diamond-legal schema', method: async () => supabase.schema('diamond-legal').from('ai_analysis_logs').insert(testData).select('id') }
    ];
    
    for (const approach of approaches) {
        try {
            console.log(`\n🔍 Testing: ${approach.name}`);
            const { data, error } = await approach.method();
            
            if (error) {
                console.log(`   ❌ Failed: ${error.message}`);
            } else {
                console.log(`   ✅ Success! Inserted with ID: ${data[0]?.id}`);
                return { success: true, method: approach.name, id: data[0]?.id };
            }
        } catch (err) {
            console.log(`   ❌ Exception: ${err.message}`);
        }
    }
    
    return { success: false };
}

async function testRPCApproach() {
    console.log('\n5️⃣ Testing RPC approach for complex operations...');
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    
    try {
        // Test if we can create a simple function to access our tables
        const { data, error } = await supabase.rpc('exec_sql', {
            sql: `SELECT COUNT(*) as table_count FROM ai_analysis_logs;`
        });
        
        if (error) {
            console.log('❌ RPC table access failed:', error.message);
            
            // Try alternative RPC approach
            const { data: data2, error: error2 } = await supabase.rpc('exec_sql', {
                sql: `SELECT COUNT(*) as table_count FROM "diamond-legal".ai_analysis_logs;`
            });
            
            if (error2) {
                console.log('❌ RPC with schema prefix also failed:', error2.message);
                return false;
            } else {
                console.log('✅ RPC with schema prefix successful');
                console.log('📊 Row count:', data2);
                return true;
            }
        } else {
            console.log('✅ RPC table access successful');
            console.log('📊 Row count:', data);
            return true;
        }
    } catch (err) {
        console.log('❌ RPC error:', err.message);
        return false;
    }
}

async function runComprehensiveDiagnostics() {
    console.log('🚀 Running comprehensive Supabase diagnostics...\n');
    
    const results = {
        discovery: null,
        schemaAccess: null,
        directAccess: null,
        insertTest: null,
        rpcTest: null
    };
    
    // Run all diagnostic tests
    results.discovery = await discoverTablesAndSchemas();
    results.schemaAccess = await testSchemaAccess();
    results.directAccess = await testDirectTableAccess();
    results.insertTest = await testInsertOperation();
    results.rpcTest = await testRPCApproach();
    
    // Summary
    console.log('\n📋 COMPREHENSIVE DIAGNOSTIC SUMMARY');
    console.log('=====================================');
    
    console.log(`Schema Discovery: ${results.discovery ? '✅ SUCCESS' : '❌ FAILED'}`);
    console.log(`Schema Access Test: ${results.schemaAccess ? '✅ COMPLETED' : '❌ FAILED'}`);
    console.log(`Direct Access Test: ${results.directAccess ? '✅ COMPLETED' : '❌ FAILED'}`);
    console.log(`Insert Test: ${results.insertTest?.success ? '✅ SUCCESS' : '❌ FAILED'}`);
    console.log(`RPC Test: ${results.rpcTest ? '✅ SUCCESS' : '❌ FAILED'}`);
    
    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    
    if (results.insertTest?.success) {
        console.log(`✅ Use "${results.insertTest.method}" approach for logging`);
    } else if (results.rpcTest) {
        console.log('✅ Use RPC approach with SQL queries for data access');
    } else {
        console.log('⚠️  Tables may need to be recreated or permissions adjusted');
    }
    
    return results;
}

// Run diagnostics
runComprehensiveDiagnostics().catch(console.error);