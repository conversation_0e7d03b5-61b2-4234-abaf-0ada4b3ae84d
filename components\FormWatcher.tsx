import { useEffect, useRef } from 'react';
import { getFormSuggestions } from '../utils/ai';
import { useToast } from './ToastContext';
import { browser } from 'wxt/browser';
import { 
    getDismissedRecommendations, 
    getPreviousRecommendations, 
    clearDismissedRecommendations,
    addDismissedRecommendation,
    getActiveDismissedRecommendations,
    markAllDismissalsAsRefreshed
} from '../utils/sessionStorage';

interface FormFieldData {
    field_name: string;
    field_value: string;
    required: boolean;
    field_id: string;
}

interface InputMetadata {
    element: HTMLInputElement;
    label: string | null;
    isSelect: boolean;
    isRequired: boolean;
    fieldId: string;
}

export default function FormWatcher() {
    const observerRef = useRef<MutationObserver | null>(null);
    const watchedInputsRef = useRef<Map<HTMLInputElement, InputMetadata>>(new Map());
    const lastAiCallRef = useRef<number>(0);
    const hasCalledGeminiRef = useRef<boolean>(false);
    const currentHighlightsRef = useRef<Map<string, HTMLElement>>(new Map());
    const { addToast, clearToasts } = useToast();

    const getInputLabel = (input: HTMLInputElement): string | null => {
        // Try to find label by 'for' attribute
        if (input.id) {
            const label = document.querySelector(`label[for="${input.id}"]`);
            if (label) {
                return label.textContent?.trim() || null;
            }
        }

        // Try to find label by name attribute
        if (input.name) {
            const label = document.querySelector(`label[for="${input.name}"]`);
            if (label) {
                return label.textContent?.trim() || null;
            }
        }

        // Try to find closest label element
        const closestLabel = input.closest('label');
        if (closestLabel) {
            return closestLabel.textContent?.trim() || null;
        }

        // Try to find preceding label sibling
        let sibling = input.previousElementSibling;
        while (sibling) {
            if (sibling.tagName === 'LABEL') {
                return sibling.textContent?.trim() || null;
            }
            sibling = sibling.previousElementSibling;
        }

        // Try to find label in parent container
        const parent = input.parentElement;
        if (parent) {
            const label = parent.querySelector('label');
            if (label) {
                return label.textContent?.trim() || null;
            }
        }

        return null;
    };

    const isInputRequired = (input: HTMLInputElement): boolean => {
        // Check HTML required attribute
        if (input.hasAttribute('required') || input.required) {
            return true;
        }

        // Check aria-required attribute
        if (input.getAttribute('aria-required') === 'true') {
            return true;
        }

        // Check for asterisk (*) in associated label text
        const labelElement = getLabelElement(input);
        if (labelElement) {
            const labelText = labelElement.textContent?.trim() || '';
            if (labelText.includes('*') || labelText.includes('Required')) {
                return true;
            }
            
            // Check for boost-danger sup element (Lawmatics required field indicator)
            const supElement = labelElement.querySelector('sup.boost-danger.b');
            if (supElement) {
                return true;
            }
        }

        // Check for "required" class on input or parent elements
        if (input.classList.contains('required') || 
            input.closest('.required') || 
            input.closest('.field-required')) {
            return true;
        }

        return false;
    };

    const getLabelElement = (input: HTMLInputElement): HTMLLabelElement | null => {
        // Try to find label by 'for' attribute
        if (input.id) {
            const label = document.querySelector(`label[for="${input.id}"]`) as HTMLLabelElement;
            if (label) return label;
        }

        if (input.name) {
            const label = document.querySelector(`label[for="${input.name}"]`) as HTMLLabelElement;
            if (label) return label;
        }

        // Try to find closest label element
        const closestLabel = input.closest('label') as HTMLLabelElement;
        if (closestLabel) return closestLabel;

        // Try to find preceding label sibling
        let sibling = input.previousElementSibling;
        while (sibling) {
            if (sibling.tagName === 'LABEL') {
                return sibling as HTMLLabelElement;
            }
            sibling = sibling.previousElementSibling;
        }

        // Try to find label in parent container
        const parent = input.parentElement;
        if (parent) {
            const label = parent.querySelector('label') as HTMLLabelElement;
            if (label) return label;
        }

        return null;
    };

    const generateFieldId = (input: HTMLInputElement, label: string | null): string => {
        // Priority 1: Use existing ID directly if it's a Lawmatics encoded ID
        if (input.id && input.id.trim() !== '') {
            // Lawmatics uses base64-encoded IDs, keep them as-is for field_id
            return input.id;
        }
        
        // Priority 2: Use name attribute if unique
        if (input.name && input.name.trim() !== '') {
            return input.name;
        }
        
        // Priority 3: Generate based on label + input type + position
        const labelText = (label || '').replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_]/g, '').toLowerCase();
        const inputType = input.type || 'input';
        const position = Array.from(document.querySelectorAll('input, textarea, select')).indexOf(input);
        
        return `field_${labelText}_${inputType}_${position}`;
    };

    const getSeverityColor = (severity: 'low' | 'medium' | 'high'): string => {
        switch (severity) {
            case 'low': return '#fbbf24'; // yellow
            case 'medium': return '#f97316'; // orange
            case 'high': return '#dc2626'; // red
            default: return '#fbbf24';
        }
    };

    const createSparkleIcon = (suggestion: any): HTMLElement => {
        const sparkle = document.createElement('div');
        sparkle.innerHTML = '✨';
        sparkle.style.cssText = `
            position: absolute;
            top: -8px;
            right: -8px;
            font-size: 16px;
            cursor: pointer;
            z-index: 10000;
            background: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 2px solid ${getSeverityColor(suggestion.severity)};
        `;
        
        // Add click handler for tooltip
        sparkle.addEventListener('click', (e) => {
            e.stopPropagation();
            showTooltip(e.target as HTMLElement, suggestion);
        });
        
        return sparkle;
    };

    const showTooltip = (anchor: HTMLElement, suggestion: any) => {
        // Remove existing tooltips
        document.querySelectorAll('.field-suggestion-tooltip').forEach(el => el.remove());
        
        const tooltip = document.createElement('div');
        tooltip.className = 'field-suggestion-tooltip';
        tooltip.style.cssText = `
            position: absolute;
            background: #1f2937;
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-size: 13px;
            max-width: 250px;
            z-index: 10001;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            line-height: 1.4;
        `;
        
        tooltip.innerHTML = `
            <div style="position: relative; padding-right: 20px;">
                <button 
                    onclick="this.closest('.field-suggestion-tooltip').dispatchEvent(new CustomEvent('close'))"
                    style="
                        position: absolute;
                        top: -8px;
                        right: -8px;
                        background: rgba(255,255,255,0.2); 
                        color: white; 
                        border: none; 
                        border-radius: 50%; 
                        width: 20px;
                        height: 20px;
                        cursor: pointer; 
                        font-size: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 1;
                    "
                    onmouseover="this.style.background='rgba(255,255,255,0.3)'"
                    onmouseout="this.style.background='rgba(255,255,255,0.2)'"
                    title="Close tooltip"
                >×</button>
                <div style="font-weight: 600; margin-bottom: 8px; color: ${getSeverityColor(suggestion.severity)};">
                    ${suggestion.priority.toUpperCase()} Priority
                </div>
                <div style="margin-bottom: 12px; line-height: 1.4;">
                    ${suggestion.suggestion}
                </div>
                <div style="display: flex; justify-content: flex-end;">
                    <button 
                        onclick="this.closest('.field-suggestion-tooltip').dispatchEvent(new CustomEvent('dismiss', { detail: { suggestion: ${JSON.stringify(suggestion).replace(/"/g, '&quot;')} } }))"
                        style="
                            background: #dc2626; 
                            color: white; 
                            border: none; 
                            border-radius: 4px; 
                            padding: 4px 8px; 
                            cursor: pointer; 
                            font-size: 11px;
                            font-weight: 500;
                        "
                        onmouseover="this.style.background='#b91c1c'"
                        onmouseout="this.style.background='#dc2626'"
                        title="Dismiss this suggestion permanently"
                    >Dismiss</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(tooltip);
        
        // Add close event listener (just closes tooltip)
        tooltip.addEventListener('close', () => {
            tooltip.remove();
            document.removeEventListener('click', removeTooltip);
        });
        
        // Add dismiss event listener (dismisses suggestion permanently)
        tooltip.addEventListener('dismiss', async (e: any) => {
            const suggestionData = e.detail.suggestion;
            try {
                // Add to dismissed recommendations
                await addDismissedRecommendation(
                    suggestionData.id,
                    suggestionData.field_id,
                    suggestionData.suggestion
                );
                
                // Remove the tooltip
                tooltip.remove();
                document.removeEventListener('click', removeTooltip);
                
                // Clear the highlight for this field
                clearFieldHighlight(suggestionData.field_id);
                
                
                // Notify sidebar to update its dismissed suggestions state
                try {
                    await browser.runtime.sendMessage({ 
                        type: 'SUGGESTION_DISMISSED_FROM_DOM',
                        suggestionId: suggestionData.id
                    });
                } catch (error) {
                    console.warn('Could not notify sidebar of dismissal:', error);
                }
                
                console.log('Dismissed suggestion from tooltip:', suggestionData.id);
            } catch (error) {
                console.error('Error dismissing suggestion from tooltip:', error);
            }
        });
        
        // Position tooltip
        const anchorRect = anchor.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        
        let left = anchorRect.left + anchorRect.width / 2 - tooltipRect.width / 2;
        let top = anchorRect.top - tooltipRect.height - 8;
        
        // Adjust if tooltip goes off screen
        if (left < 8) left = 8;
        if (left + tooltipRect.width > window.innerWidth - 8) {
            left = window.innerWidth - tooltipRect.width - 8;
        }
        if (top < 8) {
            top = anchorRect.bottom + 8;
        }
        
        tooltip.style.left = left + 'px';
        tooltip.style.top = top + 'px';
        
        // Remove tooltip when clicking elsewhere (but not when clicking buttons inside)
        const removeTooltip = (e: Event) => {
            const target = e.target as HTMLElement;
            // Don't close if clicking the anchor, tooltip content, or tooltip buttons
            if (!tooltip.contains(target) && target !== anchor) {
                tooltip.remove();
                document.removeEventListener('click', removeTooltip);
            }
        };
        setTimeout(() => document.addEventListener('click', removeTooltip), 100);
    };

    const getVisualTargetElement = (input: HTMLInputElement): HTMLElement => {
        // Check if this is a Select component
        const selectContainer = input.closest('.Select');
        if (selectContainer) {
            const selectControl = selectContainer.querySelector('.Select-control') as HTMLElement;
            if (selectControl) {
                return selectControl;
            }
        }
        
        // Check if this is a date picker
        const datePickerContainer = input.closest('.lm-datepicker-container');
        if (datePickerContainer) {
            const datePickerInput = datePickerContainer.querySelector('.lm-datepicker-input') as HTMLElement;
            if (datePickerInput) {
                return datePickerInput;
            }
        }
        
        // Check if this is a textarea
        if (input.tagName.toLowerCase() === 'textarea') {
            return input;
        }
        
        // For regular inputs, check if there's a parent FormInput container
        const formInputContainer = input.closest('.FormInput');
        if (formInputContainer) {
            const actualInput = formInputContainer.querySelector('input[type="text"], input[type="email"], input[type="tel"], input[type="number"], textarea') as HTMLElement;
            if (actualInput && actualInput !== input) {
                return actualInput;
            }
        }
        
        // Default to the input itself
        return input;
    };

    const highlightField = (fieldId: string, severity: 'low' | 'medium' | 'high', suggestion: any) => {
        // Find the input element by fieldId
        let targetInput: HTMLInputElement | null = null;
        
        watchedInputsRef.current.forEach((metadata, input) => {
            if (metadata.fieldId === fieldId) {
                targetInput = input;
            }
        });
        
        if (!targetInput) {
            console.error(`No input found for field ID: "${fieldId}"`);
            return;
        }
        
        // Get the visual element that should be highlighted
        const visualTarget = getVisualTargetElement(targetInput);
        
        // Remove existing highlight for this field
        clearFieldHighlight(fieldId);
        
        try {
            const severityColor = getSeverityColor(severity);
            
            // Store original styles
            const originalStyles = {
                border: visualTarget.style.border,
                boxShadow: visualTarget.style.boxShadow,
                outline: visualTarget.style.outline
            };
            
            // Apply border highlight directly to the element
            visualTarget.style.border = `2px solid ${severityColor}`;
            visualTarget.style.outline = 'none'; // Remove default focus outline
            visualTarget.style.boxShadow = `0 0 0 1px ${severityColor}40`; // Subtle glow effect
            
            // Find the label element to position the sparkle icon after it
            const labelElement = getLabelElement(targetInput);
            let sparkleContainer: HTMLElement | null = null;
            
            if (labelElement) {
                // Create sparkle container that will be inserted after the label
                sparkleContainer = document.createElement('span');
                sparkleContainer.style.cssText = `
                    display: inline-block;
                    margin-left: 6px;
                    vertical-align: middle;
                    position: relative;
                `;
                
                // Create sparkle icon
                const sparkle = createSparkleIcon(suggestion);
                sparkle.style.cssText = `
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 20px;
                    height: 20px;
                    font-size: 14px;
                    cursor: pointer;
                    background: white;
                    border-radius: 50%;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    border: 2px solid ${severityColor};
                    position: relative;
                    z-index: 1000;
                `;
                
                sparkleContainer.appendChild(sparkle);
                
                // Insert the sparkle container right after the label
                if (labelElement.nextSibling) {
                    labelElement.parentNode?.insertBefore(sparkleContainer, labelElement.nextSibling);
                } else {
                    labelElement.parentNode?.appendChild(sparkleContainer);
                }
            } else {
                // Fallback: create a positioned container relative to the input
                sparkleContainer = document.createElement('div');
                sparkleContainer.style.cssText = `
                    position: absolute;
                    top: -12px;
                    right: -12px;
                    z-index: 10000;
                    pointer-events: auto;
                `;
                
                // Create sparkle icon with absolute positioning fallback
                const sparkle = createSparkleIcon(suggestion);
                sparkle.style.cssText = `
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 24px;
                    height: 24px;
                    font-size: 16px;
                    cursor: pointer;
                    background: white;
                    border-radius: 50%;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    border: 2px solid ${severityColor};
                `;
                
                sparkleContainer.appendChild(sparkle);
                
                // Make the parent element position relative if it's not already
                const parentElement = visualTarget.parentElement;
                if (parentElement) {
                    const computedStyle = window.getComputedStyle(parentElement);
                    if (computedStyle.position === 'static') {
                        parentElement.style.position = 'relative';
                    }
                    parentElement.appendChild(sparkleContainer);
                }
            }
            
            // Store cleanup function
            const cleanup = () => {
                // Restore original styles
                visualTarget.style.border = originalStyles.border;
                visualTarget.style.boxShadow = originalStyles.boxShadow;
                visualTarget.style.outline = originalStyles.outline;
                
                // Remove sparkle container
                if (sparkleContainer && sparkleContainer.parentNode) {
                    sparkleContainer.parentNode.removeChild(sparkleContainer);
                }
            };
            
            // Store reference for later cleanup
            currentHighlightsRef.current.set(fieldId, { remove: cleanup } as any);
            
        } catch (error) {
            console.error(`Error creating highlight for field ${fieldId}:`, error);
        }
    };

    const clearFieldHighlight = (fieldId: string) => {
        const highlight = currentHighlightsRef.current.get(fieldId);
        if (highlight) {
            highlight.remove();
            currentHighlightsRef.current.delete(fieldId);
        }
    };

    const clearAllHighlights = () => {
        currentHighlightsRef.current.forEach((highlight) => {
            highlight.remove();
        });
        currentHighlightsRef.current.clear();
        
        // Also remove any orphaned tooltips
        document.querySelectorAll('.field-suggestion-tooltip').forEach(el => el.remove());
    };

    const applyHighlights = async (suggestions: any[]) => {
        // Get only active dismissed recommendations to filter them out
        let dismissedIds: Set<string> = new Set();
        try {
            const activeDismissedRecs = await getActiveDismissedRecommendations();
            dismissedIds = new Set(activeDismissedRecs.map(d => d.id));
        } catch (error) {
            console.error('Could not load active dismissed recommendations:', error);
        }
        
        // Filter out dismissed suggestions
        const filteredSuggestions = suggestions.filter(suggestion => !dismissedIds.has(suggestion.id));
        
        // Clear existing highlights
        clearAllHighlights();
        
        // Apply new highlights (only for non-dismissed suggestions)
        filteredSuggestions.forEach((suggestion) => {
            if (suggestion.field_id && suggestion.severity) {
                highlightField(suggestion.field_id, suggestion.severity, suggestion);
            } else {
                console.error('Suggestion missing field_id or severity:', suggestion);
            }
        });
    };

    const handleInputChange = async (event: Event) => {
        const input = event.target as HTMLInputElement;
        const metadata = watchedInputsRef.current.get(input);
        if (!metadata) return;

        const form = getAllInputValues();

        // Determine the appropriate interval based on whether Gemini has been called before
        const now = Date.now();
        const timeSinceLastCall = now - lastAiCallRef.current;

        // Use 5 seconds before first Gemini call, 30 seconds after
        const requiredInterval = hasCalledGeminiRef.current ? 30000 : 5000;

        if (timeSinceLastCall >= requiredInterval) {
            lastAiCallRef.current = now;

            try {
                // Set loading state before making the API call
                await browser.storage.local.set({ ai_loading: true });

                // Get previous recommendations and dismissed items for consistency
                const previousRecommendations = await getPreviousRecommendations();
                const dismissedRecommendations = await getDismissedRecommendations();

                const suggestions = await getFormSuggestions(form, previousRecommendations, dismissedRecommendations);

                // If we got suggestions (not the early return), mark that Gemini has been called
                if (suggestions.suggestions.length > 0 || suggestions.overall_assessment !== "Form is still being filled out. Continue gathering information.") {
                    hasCalledGeminiRef.current = true;
                    console.log('Gemini called successfully for the first time. Switching to 30-second intervals.');

                    // Store suggestions and form data in chrome storage for sidebar to access and clear loading state
                    try {
                        await browser.storage.local.set({
                            'ai_suggestions': suggestions,
                            'ai_suggestions_timestamp': Date.now(),
                            'ai_loading': false,
                            'form_data': form
                        });
                        console.log('AI suggestions and form data stored in chrome storage');
                        
                        // Mark all dismissals as no longer active since we have new suggestions
                        await markAllDismissalsAsRefreshed();
                        
                        // Apply visual highlights to form fields
                        await applyHighlights(suggestions.suggestions);
                    } catch (error) {
                        console.error('Failed to store AI suggestions:', error);
                        // Clear loading state on storage error
                        await browser.storage.local.set({ ai_loading: false });
                    }

                    // Only show toast if sidebar is closed and there are high priority suggestions
                    if (suggestions.suggestions.some(s => s.priority === 'high')) {
                        try {
                            // Check if sidebar is open by sending a message to the background script
                            const response = await browser.runtime.sendMessage({ type: 'CHECK_SIDEBAR_STATE' });
                            const isSidebarOpen = response?.isOpen || false;

                            if (!isSidebarOpen) {
                                clearToasts();
                                addToast('New AI recommendations available - Click to open sidebar');
                            }
                        } catch (error) {
                            // Don't show toast if we can't reliably determine sidebar state
                            console.warn('Could not check sidebar state, skipping toast notification:', error);
                        }
                    }
                } else {
                    // Clear loading state even for early returns
                    await browser.storage.local.set({ ai_loading: false });
                }
            } catch (error) {
                console.error('Failed to get AI suggestions:', error);
                addToast('Failed to get AI suggestions');
                // Clear loading state on error
                await browser.storage.local.set({ ai_loading: false });
            }
        }
    };

    const getAllInputValues = (): Record<string, FormFieldData | string> => {
        const inputValues: Record<string, FormFieldData | string> = {};

        // Add title first (keep as string since it's not a form field)
        inputValues["title"] = document.querySelector('h1')?.textContent?.trim() || '';

        // Get all form elements (inputs and instructions) in DOM order
        const allFormElements: Array<{element: Element, type: 'input' | 'instruction', position: number}> = [];

        // Add all inputs with their DOM positions
        watchedInputsRef.current.forEach((metadata, input) => {
            if (metadata.label) {
                const position = Array.from(document.querySelectorAll('*')).indexOf(input);
                allFormElements.push({
                    element: input,
                    type: 'input',
                    position
                });
            }
        });

        // Add all instruction fields with their DOM positions
        document.querySelectorAll<HTMLElement>('.lm-instruction-field').forEach((element) => {
            const position = Array.from(document.querySelectorAll('*')).indexOf(element);
            allFormElements.push({
                element,
                type: 'instruction',
                position
            });
        });

        // Sort by DOM position to maintain proper order
        allFormElements.sort((a, b) => a.position - b.position);

        // Process elements in correct order
        let instructionCounter = 0;
        allFormElements.forEach(({element, type}) => {
            if (type === 'input') {
                const input = element as HTMLInputElement;
                const metadata = watchedInputsRef.current.get(input);
                if (metadata?.label) {
                    let fieldValue: string;
                    
                    if (metadata.isSelect) {
                        const selectContainer = input.closest('.Select');
                        const selectedLabel = selectContainer?.querySelector('.Select-value-label');
                        fieldValue = selectedLabel?.textContent?.trim() || '';
                    } else {
                        fieldValue = input.value;
                    }

                    // Create FormFieldData object for input fields
                    const formFieldData: FormFieldData = {
                        field_name: metadata.label,
                        field_value: fieldValue,
                        required: metadata.isRequired,
                        field_id: metadata.fieldId
                    };

                    inputValues[metadata.fieldId] = formFieldData;
                }
            } else if (type === 'instruction') {
                const instructionElement = element as HTMLElement;
                inputValues[`additional-instructions-${instructionCounter}`] = instructionElement.innerText?.trim() || '';
                instructionCounter++;
            }
        });

        return inputValues;
    };

    const addInputListener = (input: HTMLInputElement) => {
        if (!watchedInputsRef.current.has(input)) {
            const label = getInputLabel(input);
            const isSelect = !!(input.closest('.Select') && input.classList.contains('Select-input'));
            
            // Also check for hidden inputs that are part of Select components
            const isHiddenSelectInput = input.type === 'hidden' && !!input.closest('.Select');

            const metadata: InputMetadata = {
                element: input,
                label,
                isSelect: isSelect || isHiddenSelectInput,
                isRequired: isInputRequired(input),
                fieldId: generateFieldId(input, label)
            };


            if (metadata.isSelect) {
                input.addEventListener('input', handleInputChange);
                input.addEventListener('change', handleInputChange);
                input.addEventListener('focus', handleInputChange);
                input.addEventListener('blur', handleInputChange);
            } else {
                input.addEventListener('change', handleInputChange);
            }
            
            watchedInputsRef.current.set(input, metadata);
        }
    };

    const removeInputListener = (input: HTMLInputElement) => {
        const metadata = watchedInputsRef.current.get(input);
        if (metadata) {
            if (metadata.isSelect) {
                input.removeEventListener('input', handleInputChange);
                input.removeEventListener('change', handleInputChange);
                input.removeEventListener('focus', handleInputChange);
                input.removeEventListener('blur', handleInputChange);
            } else {
                input.removeEventListener('change', handleInputChange);
            }
            
            watchedInputsRef.current.delete(input);
        }
    };

    const watchExistingInputs = () => {
        // Watch both inputs and textareas
        const inputs = document.querySelectorAll('input, textarea');
        
        inputs.forEach((input) => {
            addInputListener(input as HTMLInputElement);
        });
    };

    const triggerManualRefresh = async () => {
        try {
            // Set loading state immediately
            await browser.storage.local.set({ ai_loading: true });
            
            const form = getAllInputValues();
            
            // Get previous recommendations and dismissed items for consistency
            const previousRecommendations = await getPreviousRecommendations();
            const dismissedRecommendations = await getDismissedRecommendations();
            
            const suggestions = await getFormSuggestions(form, previousRecommendations, dismissedRecommendations);

            // Store suggestions, form data and clear loading state
            await browser.storage.local.set({
                'ai_suggestions': suggestions,
                'ai_suggestions_timestamp': Date.now(),
                'ai_loading': false,
                'form_data': form
            });
            console.log('Manual refresh completed successfully');
            
            // Mark all dismissals as no longer active since we have new suggestions
            await markAllDismissalsAsRefreshed();
            console.log('Marked all dismissals as refreshed');
            
            // Apply visual highlights to form fields
            console.log('🚀 About to apply highlights from triggerManualRefresh');
            console.log('📊 Suggestions to highlight:', suggestions.suggestions);
            await applyHighlights(suggestions.suggestions);

            // Only show toast if sidebar is closed and there are high priority suggestions
            if (suggestions.suggestions.some(s => s.priority === 'high')) {
                try {
                    const response = await browser.runtime.sendMessage({ type: 'CHECK_SIDEBAR_STATE' });
                    const isSidebarOpen = response?.isOpen || false;

                    if (!isSidebarOpen) {
                        clearToasts();
                        addToast('New AI recommendations available - Click to open sidebar');
                    }
                } catch (error) {
                    // Don't show toast if we can't reliably determine sidebar state
                    console.warn('Could not check sidebar state during manual refresh, skipping toast notification:', error);
                }
            }
        } catch (error) {
            console.error('Manual refresh failed:', error);
            await browser.storage.local.set({ ai_loading: false });
            addToast('Failed to refresh AI suggestions');
        }
    };

    const checkInitialFormState = async () => {
        try {
            console.log('Checking initial form state for pre-filled data...');
            
            const form = getAllInputValues();
            
            // Count filled fields (excluding instruction fields and title)
            const filledFieldsCount = Object.entries(form).filter(([key, value]) => {
                // Skip instruction fields and title
                const isInstructionField = key.includes('instruction') ||
                                         key.includes('Advanced-instructions') ||
                                         key === 'title';
                
                if (isInstructionField) return false;
                
                // Handle FormFieldData objects
                if (typeof value === 'object' && value.field_value !== undefined) {
                    return value.field_value && value.field_value.trim() !== '';
                }
                
                // Handle string values (legacy support)
                if (typeof value === 'string') {
                    return value && value.trim() !== '' && 
                           !value.includes('<ul') && 
                           !value.includes('<li') && 
                           !value.includes('<p');
                }
                
                return false;
            }).length;
            
            console.log(`Initial form check: ${filledFieldsCount} fields filled`);
            
            // If 6 or more fields are filled, trigger AI analysis immediately
            if (filledFieldsCount >= 6) {
                console.log('Form has sufficient data for initial AI analysis, triggering...');
                
                // Set loading state
                await browser.storage.local.set({ ai_loading: true });
                
                // Get previous recommendations and dismissed items for consistency
                const previousRecommendations = await getPreviousRecommendations();
                const dismissedRecommendations = await getDismissedRecommendations();
                
                const suggestions = await getFormSuggestions(form, previousRecommendations, dismissedRecommendations);
                
                // Mark that Gemini has been called so subsequent calls use 30-second intervals
                hasCalledGeminiRef.current = true;
                
                // Store suggestions, form data and clear loading state
                await browser.storage.local.set({
                    'ai_suggestions': suggestions,
                    'ai_suggestions_timestamp': Date.now(),
                    'ai_loading': false,
                    'form_data': form
                });
                console.log('Initial AI analysis completed successfully');
                
                // Mark all dismissals as no longer active since we have new suggestions
                await markAllDismissalsAsRefreshed();
                console.log('Marked all dismissals as refreshed');
                
                // Apply visual highlights to form fields
                console.log('🚀 About to apply highlights from checkInitialFormState');
                console.log('📊 Suggestions to highlight:', suggestions.suggestions);
                await applyHighlights(suggestions.suggestions);
                
                // Only show toast if sidebar is closed and there are high priority suggestions
                if (suggestions.suggestions.some(s => s.priority === 'high')) {
                    try {
                        const response = await browser.runtime.sendMessage({ type: 'CHECK_SIDEBAR_STATE' });
                        const isSidebarOpen = response?.isOpen || false;

                        if (!isSidebarOpen) {
                            clearToasts();
                            addToast('AI recommendations ready - Click to open sidebar');
                        }
                    } catch (error) {
                        // Don't show toast if we can't reliably determine sidebar state
                        console.warn('Could not check sidebar state during initial analysis, skipping toast notification:', error);
                    }
                }
            } else {
                console.log('Form does not have enough filled fields for initial analysis');
            }
        } catch (error) {
            console.error('Initial form state check failed:', error);
            // Clear loading state on error
            await browser.storage.local.set({ ai_loading: false });
        }
    };

    useEffect(() => {
        // Clear previous recommendations when form loads/refreshes
        const clearPreviousRecommendations = async () => {
            try {
                await browser.storage.local.remove(['ai_suggestions', 'ai_suggestions_timestamp', 'ai_loading']);
                // Clear dismissed recommendations for current URL
                await clearDismissedRecommendations();
                console.log('Cleared previous AI recommendations and dismissed items on form load');
                
                // Clear visual highlights
                clearAllHighlights();
            } catch (error) {
                console.error('Failed to clear previous recommendations:', error);
            }
        };

        // Clear recommendations immediately when component mounts (form loads)
        clearPreviousRecommendations();

        // Listen for navigation events to clear recommendations on form changes
        const handleNavigation = () => {
            console.log('Navigation detected, clearing recommendations');
            clearPreviousRecommendations();
        };

        // Listen for various navigation events
        window.addEventListener('beforeunload', handleNavigation);
        window.addEventListener('popstate', handleNavigation);

        // Also listen for URL changes (for SPAs)
        let currentUrl = window.location.href;
        const urlChangeInterval = setInterval(() => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                console.log('URL change detected, clearing recommendations and re-analyzing form');
                clearPreviousRecommendations();
                
                // Re-watch inputs after URL change (new form may have loaded)
                setTimeout(() => {
                    watchExistingInputs();
                    // Check for pre-filled data in the new form after a delay
                    setTimeout(() => {
                        checkInitialFormState();
                    }, 1500); // Additional delay to ensure new form is fully loaded
                }, 500); // Small delay to ensure DOM changes have settled
            }
        }, 1000);

        // Watch existing inputs
        watchExistingInputs();

        // Check initial form state after a short delay to ensure DOM is fully loaded
        setTimeout(() => {
            checkInitialFormState();
        }, 1500); // 1.5 second delay to ensure form is fully rendered

        // Create mutation observer to watch for new inputs and textareas
        observerRef.current = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                // Check for added nodes
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const element = node as Element;
                        
                        // Check if the added node is an input or textarea
                        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                            addInputListener(element as HTMLInputElement);
                        }
                        
                        // Check for inputs and textareas within the added node
                        const inputs = element.querySelectorAll('input, textarea');
                        inputs.forEach(input => addInputListener(input as HTMLInputElement));
                    }
                });

                // Check for removed nodes
                mutation.removedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const element = node as Element;
                        
                        // Check if the removed node is an input or textarea
                        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                            removeInputListener(element as HTMLInputElement);
                        }
                        
                        // Check for inputs and textareas within the removed node
                        const inputs = element.querySelectorAll('input, textarea');
                        inputs.forEach(input => removeInputListener(input as HTMLInputElement));
                    }
                });
            });
        });

        // Start observing
        observerRef.current.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Listen for manual refresh messages from sidebar
        const handleMessage = (message: any) => {
            if (message.type === 'MANUAL_REFRESH') {
                console.log('Received manual refresh request');
                triggerManualRefresh();
            } else if (message.type === 'DISMISS_SUGGESTION') {
                console.log('Received dismiss suggestion request for field:', message.fieldId);
                // Clear highlight for the specific field using the proper cleanup function
                if (message.fieldId) {
                    clearFieldHighlight(message.fieldId);
                }
            }
        };

        browser.runtime.onMessage.addListener(handleMessage);

        // Cleanup function
        return () => {
            if (observerRef.current) {
                observerRef.current.disconnect();
            }

            // Remove navigation event listeners
            window.removeEventListener('beforeunload', handleNavigation);
            window.removeEventListener('popstate', handleNavigation);
            clearInterval(urlChangeInterval);

            // Remove message listener
            browser.runtime.onMessage.removeListener(handleMessage);
            
            // Clear all visual highlights
            clearAllHighlights();

            // Remove all input event listeners
            watchedInputsRef.current.forEach((metadata, input) => {
                removeInputListener(input);
            });
            watchedInputsRef.current.clear();
        };
    }, []);

    return (
        <div></div>
    );
}
