import { createClient } from '@supabase/supabase-js';
import { FormSuggestion, DismissedRecommendation } from './ai';

const SUPABASE_URL = 'https://vgjhxvpxooboinitmwrg.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZnamh4dnB4b29ib2luaXRtd3JnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcxNTI3Njc3NSwiZXhwIjoyMDMwODUyNzc1fQ.-hsT_nvM2By1bDV5H03KvXNxEERbuzSK3-E2QMi9cT0';

// Initialize Supabase client for logging
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

export interface AIAnalysisLogData {
  // Session Context
  sessionId: string;
  formUrl: string;
  extensionVersion?: string;
  modelName: string;
  userAgent?: string;
  
  // Input State
  formData: Record<string, any>;
  filledFieldsCount: number;
  triggerReason: string;
  previousRecommendations: FormSuggestion[];
  dismissedRecommendations: DismissedRecommendation[];
  
  // AI Interaction
  fullPrompt: any[];
  modelParameters: {
    temperature: number;
    reasoning?: any;
  };
  toolCalls: Array<{
    name: string;
    arguments: any;
    response: string;
    executionTime: number;
  }>;
  iterations: number;
  rawAIResponse: any;
  reasoningTokens?: number;
  
  // Performance
  totalDuration: number;
  apiResponseTime: number;
  promptTokens?: number;
  completionTokens?: number;
  
  // Results
  suggestions: FormSuggestion[];
  overallAssessment: string;
  
  // Status
  success: boolean;
  errors?: string[];
}

export interface UserInteractionData {
  analysisLogId: number;
  interactionType: 'dismissed' | 'acted_upon' | 'ignored';
  suggestionId?: string;
  details?: Record<string, any>;
}

class AILogger {
  private static instance: AILogger;
  private sessionId: string;
  private extensionVersion: string;
  
  private constructor() {
    this.sessionId = this.generateSessionId();
    this.extensionVersion = this.getExtensionVersion();
  }
  
  public static getInstance(): AILogger {
    if (!AILogger.instance) {
      AILogger.instance = new AILogger();
    }
    return AILogger.instance;
  }
  
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  private getExtensionVersion(): string {
    try {
      // Try to get version from manifest in browser extension context
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest) {
        return chrome.runtime.getManifest().version;
      }
      return 'unknown';
    } catch {
      return 'unknown';
    }
  }
  
  public async logAIAnalysis(data: Omit<AIAnalysisLogData, 'sessionId' | 'extensionVersion'>): Promise<number | null> {
    try {
      const logEntry = {
        session_id: this.sessionId,
        created_at: new Date().toISOString(),
        form_url: data.formUrl,
        extension_version: this.extensionVersion,
        model_name: data.modelName,
        user_agent: data.userAgent || navigator.userAgent,
        form_data: data.formData,
        filled_fields_count: data.filledFieldsCount,
        trigger_reason: data.triggerReason,
        previous_recommendations: data.previousRecommendations,
        dismissed_recommendations: data.dismissedRecommendations,
        full_prompt: data.fullPrompt,
        model_parameters: data.modelParameters,
        tool_calls: data.toolCalls,
        iterations: data.iterations,
        raw_ai_response: data.rawAIResponse,
        reasoning_tokens: data.reasoningTokens,
        total_duration_ms: data.totalDuration,
        api_response_time_ms: data.apiResponseTime,
        prompt_tokens: data.promptTokens,
        completion_tokens: data.completionTokens,
        suggestions: data.suggestions,
        overall_assessment: data.overallAssessment,
        success: data.success,
        errors: data.errors || []
      };
      
      console.log('🔄 Logging AI analysis to Supabase...', {
        sessionId: this.sessionId,
        formUrl: data.formUrl,
        modelName: data.modelName,
        success: data.success,
        suggestionsCount: data.suggestions.length
      });
      
      const { data: result, error } = await supabase
        .schema('diamond-legal')
        .from('ai_analysis_logs')
        .insert(logEntry)
        .select('id')
        .single();
      
      if (error) {
        console.error('❌ Failed to log AI analysis:', error);
        return null;
      }
      
      console.log('✅ AI analysis logged successfully:', result.id);
      return result.id;
    } catch (error) {
      console.error('❌ Error logging AI analysis:', error);
      return null;
    }
  }
  
  public async logUserInteraction(data: UserInteractionData): Promise<boolean> {
    try {
      const { error } = await supabase
        .schema('diamond-legal')
        .from('user_interactions')
        .insert({
          analysis_log_id: data.analysisLogId,
          interaction_type: data.interactionType,
          suggestion_id: data.suggestionId,
          details: data.details || {}
        });
      
      if (error) {
        console.error('❌ Failed to log user interaction:', error);
        return false;
      }
      
      console.log('✅ User interaction logged:', data.interactionType);
      return true;
    } catch (error) {
      console.error('❌ Error logging user interaction:', error);
      return false;
    }
  }
  
  public async updateFormMetadata(formUrl: string, formTitle?: string, formType?: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .schema('diamond-legal')
        .from('form_metadata')
        .upsert({
          form_url: formUrl,
          form_title: formTitle,
          form_type: formType,
          updated_at: new Date().toISOString()
        }, { 
          onConflict: 'form_url' 
        });
      
      if (error) {
        console.error('❌ Failed to update form metadata:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('❌ Error updating form metadata:', error);
      return false;
    }
  }
  
  public getSessionId(): string {
    return this.sessionId;
  }
  
  public newSession(): void {
    this.sessionId = this.generateSessionId();
    console.log('🔄 New logging session started:', this.sessionId);
  }
  
  // Helper method to create performance tracker
  public createPerformanceTracker() {
    const startTime = performance.now();
    const toolCalls: Array<{name: string; arguments: any; response: string; executionTime: number}> = [];
    
    return {
      trackToolCall: (name: string, args: any, response: string, executionTime: number) => {
        toolCalls.push({ name, arguments: args, response, executionTime });
      },
      
      getToolCalls: () => toolCalls,
      
      getTotalDuration: () => Math.round(performance.now() - startTime),
      
      finish: () => ({
        totalDuration: Math.round(performance.now() - startTime),
        toolCalls
      })
    };
  }
}

// Export singleton instance
export const aiLogger = AILogger.getInstance();

// Helper function to safely log without blocking execution
export async function safeLogAIAnalysis(data: Omit<AIAnalysisLogData, 'sessionId' | 'extensionVersion'>): Promise<number | null> {
  try {
    return await aiLogger.logAIAnalysis(data);
  } catch (error) {
    console.warn('⚠️ AI logging failed but continuing execution:', error);
    return null;
  }
}

export async function safeLogUserInteraction(data: UserInteractionData): Promise<boolean> {
  try {
    return await aiLogger.logUserInteraction(data);
  } catch (error) {
    console.warn('⚠️ User interaction logging failed but continuing execution:', error);
    return false;
  }
}