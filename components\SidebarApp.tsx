import { useEffect, useState } from 'react';
import { browser } from 'wxt/browser';
import { FormSuggestion, FormFeedback } from '../utils/ai';
import { addDismissedRecommendation, getActiveDismissedRecommendations, markAllDismissalsAsRefreshed } from '../utils/sessionStorage';

interface FormFieldData {
    field_name: string;
    field_value: string;
    required: boolean;
    field_id: string;
}

export default function SidebarApp() {
    const [feedback, setFeedback] = useState<FormFeedback | null>(null);
    const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [formData, setFormData] = useState<Record<string, FormFieldData | string>>({});
    const [dismissedSuggestions, setDismissedSuggestions] = useState<Set<string>>(new Set());

    const handleRefreshClick = async () => {
        try {
            // Send message to content script to trigger manual refresh
            const tabs = await browser.tabs.query({ active: true, currentWindow: true });
            if (tabs[0]?.id) {
                await browser.tabs.sendMessage(tabs[0].id, { type: 'MANUAL_REFRESH' });
            }
        } catch (error) {
            console.error('Failed to send manual refresh message:', error);
        }
    };

    const handleDismissSuggestion = async (suggestion: FormSuggestion) => {
        try {
            // Add to dismissed recommendations in session storage
            await addDismissedRecommendation(
                suggestion.id,
                suggestion.field_id,
                suggestion.suggestion
            );
            
            // Add to local dismissed set for immediate UI update
            setDismissedSuggestions(prev => new Set(prev).add(suggestion.id));
            
            
            // Send message to content script to clear highlight for this field
            const tabs = await browser.tabs.query({ active: true, currentWindow: true });
            if (tabs[0]?.id) {
                await browser.tabs.sendMessage(tabs[0].id, { 
                    type: 'DISMISS_SUGGESTION', 
                    fieldId: suggestion.field_id,
                    suggestionId: suggestion.id
                });
            }
            
            console.log('Dismissed suggestion:', suggestion.id);
        } catch (error) {
            console.error('Failed to dismiss suggestion:', error);
        }
    };

    useEffect(() => {
        // Report that sidebar has opened with retry logic
        const reportSidebarOpened = async () => {
            let retries = 3;
            while (retries > 0) {
                try {
                    await browser.runtime.sendMessage({ type: 'SIDEBAR_OPENED' });
                    console.log('Successfully reported sidebar opened');
                    break;
                } catch (error) {
                    retries--;
                    console.warn(`Failed to report sidebar opened, retries left: ${retries}`, error);
                    if (retries > 0) {
                        // Wait 100ms before retrying
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                }
            }
        };
        
        reportSidebarOpened();

        // Function to load suggestions from storage (initial load only)
        const loadInitialSuggestions = async () => {
            try {
                const result = await browser.storage.local.get(['ai_suggestions', 'ai_suggestions_timestamp', 'ai_loading', 'form_data']);
                
                // Load only active dismissed recommendations from session storage
                const activeDismissedRecs = await getActiveDismissedRecommendations();
                const dismissedIds = new Set(activeDismissedRecs.map(d => d.id));
                setDismissedSuggestions(dismissedIds);
                
                if (result.ai_suggestions) {
                    setFeedback(result.ai_suggestions);
                    if (result.ai_suggestions_timestamp) {
                        setLastUpdated(new Date(result.ai_suggestions_timestamp));
                    }
                }
                if (result.form_data) {
                    setFormData(result.form_data);
                }
                if (result.ai_loading !== undefined) {
                    setIsLoading(result.ai_loading);
                }
            } catch (error) {
                console.error('Failed to load AI suggestions from storage:', error);
            }
        };

        // Function for polling that doesn't override dismissed state
        const pollForUpdates = async () => {
            try {
                const result = await browser.storage.local.get(['ai_suggestions', 'ai_suggestions_timestamp', 'ai_loading', 'form_data']);
                
                // Only update suggestions and form data, preserve dismissed state
                if (result.ai_suggestions) {
                    setFeedback(result.ai_suggestions);
                    if (result.ai_suggestions_timestamp) {
                        setLastUpdated(new Date(result.ai_suggestions_timestamp));
                    }
                }
                if (result.form_data) {
                    setFormData(result.form_data);
                }
                if (result.ai_loading !== undefined) {
                    setIsLoading(result.ai_loading);
                }
            } catch (error) {
                console.error('Failed to poll for updates:', error);
            }
        };

        // Load suggestions immediately
        loadInitialSuggestions();

        // Listen for storage changes
        const handleStorageChange = async (changes: any) => {
            if (changes.ai_suggestions) {
                // Handle both new suggestions and cleared suggestions
                if (changes.ai_suggestions.newValue === undefined) {
                    // Suggestions were cleared
                    setFeedback(null);
                    setLastUpdated(null);
                    setIsLoading(false);
                    console.log('AI suggestions cleared from storage');
                } else {
                    // New suggestions received - clear all dismissed suggestions since we have fresh data
                    setDismissedSuggestions(new Set());
                    
                    setFeedback(changes.ai_suggestions.newValue);
                    setIsLoading(false); // Stop loading when we get new suggestions
                    console.log('AI suggestions updated from storage:', changes.ai_suggestions.newValue);
                    console.log('Cleared dismissed suggestions for fresh AI data');
                }
            }
            if (changes.form_data) {
                if (changes.form_data.newValue) {
                    setFormData(changes.form_data.newValue);
                }
            }
            if (changes.ai_suggestions_timestamp) {
                if (changes.ai_suggestions_timestamp.newValue === undefined) {
                    setLastUpdated(null);
                } else {
                    setLastUpdated(new Date(changes.ai_suggestions_timestamp.newValue));
                }
            }
            if (changes.ai_loading) {
                setIsLoading(changes.ai_loading.newValue);
                console.log('AI loading state changed:', changes.ai_loading.newValue);
            }
        };

        browser.storage.onChanged.addListener(handleStorageChange);

        // Listen for dismissal messages from DOM
        const handleMessage = async (message: any) => {
            if (message.type === 'SUGGESTION_DISMISSED_FROM_DOM') {
                console.log('Received dismissal notification from DOM:', message.suggestionId);
                // Add to local dismissed set for immediate UI update
                setDismissedSuggestions(prev => new Set(prev).add(message.suggestionId));
            }
        };
        browser.runtime.onMessage.addListener(handleMessage);

        // Poll for updates every 2 seconds as a fallback (but preserve dismissed state)
        const pollInterval = setInterval(pollForUpdates, 2000);

        return () => {
            // Report that sidebar is closing with retry logic
            const reportSidebarClosed = async () => {
                let retries = 3;
                while (retries > 0) {
                    try {
                        await browser.runtime.sendMessage({ type: 'SIDEBAR_CLOSED' });
                        console.log('Successfully reported sidebar closed');
                        break;
                    } catch (error) {
                        retries--;
                        console.warn(`Failed to report sidebar closed, retries left: ${retries}`, error);
                        if (retries > 0) {
                            // Wait 50ms before retrying
                            await new Promise(resolve => setTimeout(resolve, 50));
                        }
                    }
                }
            };
            
            reportSidebarClosed();

            browser.storage.onChanged.removeListener(handleStorageChange);
            browser.runtime.onMessage.removeListener(handleMessage);
            clearInterval(pollInterval);
        };
    }, []);

    const getFieldNameFromId = (fieldId: string): string => {
        // Try to find the field name from the form data
        const fieldData = formData[fieldId];
        if (fieldData && typeof fieldData === 'object' && fieldData.field_name) {
            return fieldData.field_name;
        }
        
        // Enhanced fallback: better field ID to readable name conversion
        let displayName = fieldId;
        
        // Remove common prefixes
        displayName = displayName.replace(/^field_/, '');
        
        // Handle base64-encoded Lawmatics IDs by trying to decode them
        try {
            if (displayName.match(/^[A-Za-z0-9+/=]+$/)) {
                const decoded = atob(displayName);
                if (decoded && decoded.length < 100) { // Reasonable field name length
                    displayName = decoded;
                }
            }
        } catch (e) {
            // Not base64, continue with original
        }
        
        // Convert underscores to spaces and capitalize
        displayName = displayName
            .replace(/_/g, ' ')
            .replace(/([a-z])([A-Z])/g, '$1 $2') // camelCase to spaces
            .toLowerCase()
            .replace(/\b\w/g, l => l.toUpperCase()); // capitalize words
        
        return displayName;
    };

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'high': return '#dc2626';
            case 'medium': return '#ea580c';
            case 'low': return '#16a34a';
            default: return '#6b7280';
        }
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case 'missing_info': return '❗';
            case 'follow_up_question': return '❓';
            case 'legal_consideration': return '⚖️';
            case 'documentation_needed': return '📄';
            default: return '💡';
        }
    };

    return (
        <div style={{ 
            padding: '16px', 
            fontFamily: 'system-ui, -apple-system, sans-serif',
            height: '100vh',
            overflow: 'auto'
        }}>
            <div style={{
                marginBottom: '20px',
                borderBottom: '2px solid #1e3a8a',
                paddingBottom: '12px'
            }}>
                <h1 style={{
                    margin: '0 0 8px 0',
                    fontSize: '20px',
                    color: '#1e3a8a',
                    fontWeight: 'bold'
                }}>
                    Prime Legal Assistant
                </h1>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                        {lastUpdated && !isLoading && (
                            <p style={{
                                margin: '0',
                                fontSize: '12px',
                                color: '#6b7280'
                            }}>
                                Last updated: {lastUpdated.toLocaleTimeString()}
                            </p>
                        )}
                        {isLoading && (
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '8px',
                                margin: '0',
                                fontSize: '12px',
                                color: '#1e3a8a',
                                fontWeight: '500'
                            }}>
                                <div style={{
                                    width: '12px',
                                    height: '12px',
                                    border: '2px solid #e5e7eb',
                                    borderTop: '2px solid #1e3a8a',
                                    borderRadius: '50%',
                                    animation: 'spin 1s linear infinite'
                                }}></div>
                                Refreshing recommendations...
                            </div>
                        )}
                    </div>
                    <button
                        onClick={handleRefreshClick}
                        disabled={isLoading}
                        style={{
                            padding: '6px',
                            border: 'none',
                            borderRadius: '4px',
                            backgroundColor: isLoading ? '#f3f4f6' : '#1e3a8a',
                            color: isLoading ? '#9ca3af' : 'white',
                            cursor: isLoading ? 'not-allowed' : 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'all 0.2s ease',
                            boxShadow: isLoading ? 'none' : '0 1px 2px rgba(0, 0, 0, 0.1)'
                        }}
                        title="Refresh recommendations"
                    >
                        {isLoading ? (
                            <div style={{
                                width: '14px',
                                height: '14px',
                                border: '2px solid #e5e7eb',
                                borderTop: '2px solid #9ca3af',
                                borderRadius: '50%',
                                animation: 'spin 1s linear infinite'
                            }}></div>
                        ) : (
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                            </svg>
                        )}
                    </button>
                </div>
            </div>

            <style>
                {`
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                `}
            </style>

            {!feedback ? (
                <div style={{ 
                    textAlign: 'center', 
                    color: '#6b7280',
                    marginTop: '40px'
                }}>
                    <p>Waiting for form analysis...</p>
                    <p style={{ fontSize: '12px' }}>
                        Fill out at least 6 fields to get AI suggestions
                    </p>
                </div>
            ) : (
                <div>
                    {/* Overall Assessment */}
                    <div style={{ 
                        marginBottom: '20px',
                        padding: '12px',
                        backgroundColor: '#f8fafc',
                        borderRadius: '8px',
                        border: '1px solid #e2e8f0'
                    }}>
                        <h3 style={{ 
                            margin: '0 0 8px 0', 
                            fontSize: '14px', 
                            color: '#374151',
                            fontWeight: '600'
                        }}>
                            Assessment
                        </h3>
                        <p style={{ 
                            margin: '0', 
                            fontSize: '13px', 
                            lineHeight: '1.5',
                            color: '#4b5563'
                        }}>
                            {feedback.overall_assessment}
                        </p>
                    </div>

                    {/* Suggestions */}
                    {feedback.suggestions.length > 0 && (
                        <div style={{ marginBottom: '20px' }}>
                            <h3 style={{ 
                                margin: '0 0 12px 0', 
                                fontSize: '16px', 
                                color: '#374151',
                                fontWeight: '600'
                            }}>
                                Suggestions ({feedback.suggestions.filter(s => !dismissedSuggestions.has(s.id)).length})
                            </h3>
                            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                {feedback.suggestions
                                    .filter(suggestion => !dismissedSuggestions.has(suggestion.id))
                                    .map((suggestion, index) => (
                                    <div 
                                        key={suggestion.id || index}
                                        style={{ 
                                            padding: '12px',
                                            backgroundColor: '#ffffff',
                                            borderRadius: '6px',
                                            border: `2px solid ${getPriorityColor(suggestion.priority)}`,
                                            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                                            position: 'relative'
                                        }}
                                    >
                                        <div style={{ 
                                            display: 'flex', 
                                            alignItems: 'center', 
                                            marginBottom: '6px',
                                            gap: '6px'
                                        }}>
                                            <span style={{ fontSize: '14px' }}>
                                                {getTypeIcon(suggestion.type)}
                                            </span>
                                            <span style={{ 
                                                fontSize: '11px', 
                                                fontWeight: '600',
                                                color: getPriorityColor(suggestion.priority),
                                                textTransform: 'uppercase',
                                                letterSpacing: '0.5px'
                                            }}>
                                                {suggestion.priority}
                                            </span>
                                            <span style={{ 
                                                fontSize: '11px', 
                                                color: '#6b7280',
                                                backgroundColor: '#f3f4f6',
                                                padding: '2px 6px',
                                                borderRadius: '4px'
                                            }}>
                                                {getFieldNameFromId(suggestion.field_id)}
                                            </span>
                                            <button
                                                onClick={() => handleDismissSuggestion(suggestion)}
                                                style={{
                                                    marginLeft: 'auto',
                                                    padding: '2px 6px',
                                                    border: 'none',
                                                    borderRadius: '4px',
                                                    backgroundColor: '#f3f4f6',
                                                    color: '#6b7280',
                                                    cursor: 'pointer',
                                                    fontSize: '12px',
                                                    fontWeight: '500',
                                                    transition: 'all 0.2s ease'
                                                }}
                                                onMouseOver={(e) => {
                                                    e.currentTarget.style.backgroundColor = '#e5e7eb';
                                                    e.currentTarget.style.color = '#374151';
                                                }}
                                                onMouseOut={(e) => {
                                                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                                                    e.currentTarget.style.color = '#6b7280';
                                                }}
                                                title="Dismiss this suggestion"
                                            >
                                                ×
                                            </button>
                                        </div>
                                        <p style={{ 
                                            margin: '0', 
                                            fontSize: '13px', 
                                            lineHeight: '1.4',
                                            color: '#374151'
                                        }}>
                                            {suggestion.suggestion}
                                        </p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
}
