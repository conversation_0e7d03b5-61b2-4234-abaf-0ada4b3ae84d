import { browser } from 'wxt/browser';

export default defineBackground(() => {
    // Track sidebar state per tab
    const sidebarState = new Map<number, boolean>();
    
    // Helper function to get dismissed storage key
    const getDismissedStorageKey = (url: string): string => {
        return `dismissed_recommendations_${url}`;
    };

    // Handle messages from content script and sidebar
    browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
        try {
            if (message.type === 'OPEN_SIDEBAR') {
                // Open the sidebar for the current tab
                if (sender.tab?.id) {
                    const tabId = sender.tab.id;
                    browser.sidePanel?.open({ tabId }).then(() => {
                        sidebarState.set(tabId, true);
                        console.log(`Sidebar opened for tab ${tabId}`);
                    }).catch((error) => {
                        console.error(`Failed to open sidebar for tab ${tabId}:`, error);
                    });
                }
            } else if (message.type === 'CHECK_SIDEBAR_STATE') {
                // Return the current sidebar state for the tab
                const tabId = sender.tab?.id;
                if (!tabId) {
                    console.warn('CHECK_SIDEBAR_STATE: No tab ID available');
                    sendResponse({ isOpen: false });
                    return true;
                }
                
                const isOpen = sidebarState.get(tabId) || false;
                console.log(`Sidebar state check for tab ${tabId}: ${isOpen}`);
                sendResponse({ isOpen });
                return true; // Keep the message channel open for async response
            } else if (message.type === 'SIDEBAR_OPENED') {
                // Sidebar reports it has opened
                if (sender.tab?.id) {
                    const tabId = sender.tab.id;
                    sidebarState.set(tabId, true);
                    console.log(`Sidebar state set to OPEN for tab ${tabId}`);
                } else {
                    console.warn('SIDEBAR_OPENED: No tab ID available');
                }
            } else if (message.type === 'SIDEBAR_CLOSED') {
                // Sidebar reports it has closed
                if (sender.tab?.id) {
                    const tabId = sender.tab.id;
                    sidebarState.set(tabId, false);
                    console.log(`Sidebar state set to CLOSED for tab ${tabId}`);
                } else {
                    console.warn('SIDEBAR_CLOSED: No tab ID available');
                }
            } else if (message.type === 'GET_DISMISSED_RECOMMENDATIONS') {
                // Get dismissed recommendations from session storage
                const key = getDismissedStorageKey(message.url);
                browser.storage.session.get(key).then((result) => {
                    sendResponse({ dismissedRecommendations: result[key] || [] });
                }).catch((error) => {
                    console.error('Error getting dismissed recommendations:', error);
                    sendResponse({ dismissedRecommendations: [] });
                });
                return true; // Keep message channel open for async response
            } else if (message.type === 'ADD_DISMISSED_RECOMMENDATION') {
                // Add dismissed recommendation to session storage
                const key = getDismissedStorageKey(message.url);
                browser.storage.session.get(key).then(async (result) => {
                    const currentDismissed = result[key] || [];
                    const existing = currentDismissed.find((item: any) => item.id === message.suggestion.id);
                    if (!existing) {
                        const suggestionWithFlag = {
                            ...message.suggestion,
                            dismissed_since_last_refresh: true
                        };
                        currentDismissed.push(suggestionWithFlag);
                        await browser.storage.session.set({ [key]: currentDismissed });
                    }
                    sendResponse({ success: true });
                }).catch((error) => {
                    console.error('Error adding dismissed recommendation:', error);
                    sendResponse({ success: false, error: error.message });
                });
                return true; // Keep message channel open for async response
            } else if (message.type === 'CLEAR_DISMISSED_RECOMMENDATIONS') {
                // Clear dismissed recommendations from session storage
                const key = getDismissedStorageKey(message.url);
                browser.storage.session.remove(key).then(() => {
                    sendResponse({ success: true });
                }).catch((error) => {
                    console.error('Error clearing dismissed recommendations:', error);
                    sendResponse({ success: false, error: error.message });
                });
                return true; // Keep message channel open for async response
            } else if (message.type === 'MARK_DISMISSALS_AS_REFRESHED') {
                // Mark all dismissed recommendations as no longer active since refresh
                const key = getDismissedStorageKey(message.url);
                browser.storage.session.get(key).then(async (result) => {
                    const currentDismissed = result[key] || [];
                    const updatedDismissed = currentDismissed.map((item: any) => ({
                        ...item,
                        dismissed_since_last_refresh: false
                    }));
                    await browser.storage.session.set({ [key]: updatedDismissed });
                    sendResponse({ success: true });
                }).catch((error) => {
                    console.error('Error marking dismissals as refreshed:', error);
                    sendResponse({ success: false, error: error.message });
                });
                return true; // Keep message channel open for async response
            }
        } catch (error) {
            console.error('Error handling runtime message:', error, message);
            // For CHECK_SIDEBAR_STATE, ensure we always send a response
            if (message.type === 'CHECK_SIDEBAR_STATE') {
                sendResponse({ isOpen: false });
            }
        }
    });

    // Clean up state when tabs are closed
    browser.tabs?.onRemoved?.addListener((tabId) => {
        sidebarState.delete(tabId);
    });
});
