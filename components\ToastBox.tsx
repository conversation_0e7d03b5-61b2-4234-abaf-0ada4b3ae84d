import { useToast } from './ToastContext';
import Toast from './Toast';

export default function ToastBox() {
    const { toasts, removeToast } = useToast();

    return (
        <div
            style={{
                position: 'fixed',
                bottom: '20px',
                right: '20px',
                zIndex: 1000,
                display: 'flex',
                flexDirection: 'column',
                gap: '8px',
                pointerEvents: 'none',
            }}
        >
            {toasts.map(toast => (
                <div key={toast.id} style={{ pointerEvents: 'auto' }}>
                    <Toast
                        message={toast.message}
                        onDismiss={() => removeToast(toast.id)}
                        visible={true}
                    />
                </div>
            ))}
        </div>
    );
}
