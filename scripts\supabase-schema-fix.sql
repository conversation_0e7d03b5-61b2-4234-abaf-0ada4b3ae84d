-- Supabase Schema Fix for AI Logging
-- This script provides two solutions for the schema access issue

-- ============================================================================
-- SOLUTION 1: Move tables to public schema (RECOMMENDED)
-- ============================================================================

-- Drop existing tables in diamond-legal schema if they exist
DROP TABLE IF EXISTS "diamond-legal".user_interactions;
DROP TABLE IF EXISTS "diamond-legal".ai_analysis_logs;
DROP TABLE IF EXISTS "diamond-legal".form_metadata;

-- Create tables in public schema (accessible via Supabase REST API)
CREATE TABLE IF NOT EXISTS public.ai_analysis_logs (
  id BIGSERIAL PRIMARY KEY,
  session_id TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Context
  form_url TEXT NOT NULL,
  extension_version TEXT,
  model_name TEXT NOT NULL,
  user_agent TEXT,
  
  -- Input State
  form_data JSONB NOT NULL,
  filled_fields_count INTEGER,
  trigger_reason TEXT,
  previous_recommendations JSONB DEFAULT '[]'::jsonb,
  dismissed_recommendations JSONB DEFAULT '[]'::jsonb,
  
  -- AI Interaction
  full_prompt JSONB NOT NULL,
  model_parameters JSONB,
  tool_calls JSONB DEFAULT '[]'::jsonb,
  iterations INTEGER DEFAULT 1,
  raw_ai_response JSONB,
  reasoning_tokens INTEGER,
  
  -- Performance
  total_duration_ms INTEGER,
  api_response_time_ms INTEGER,
  prompt_tokens INTEGER,
  completion_tokens INTEGER,
  
  -- Results
  suggestions JSONB DEFAULT '[]'::jsonb,
  overall_assessment TEXT,
  
  -- Status
  success BOOLEAN DEFAULT TRUE,
  errors JSONB DEFAULT '[]'::jsonb
);

CREATE TABLE IF NOT EXISTS public.user_interactions (
  id BIGSERIAL PRIMARY KEY,
  analysis_log_id BIGINT REFERENCES public.ai_analysis_logs(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  interaction_type TEXT NOT NULL,
  suggestion_id TEXT,
  details JSONB
);

CREATE TABLE IF NOT EXISTS public.form_metadata (
  id BIGSERIAL PRIMARY KEY,
  form_url TEXT UNIQUE NOT NULL,
  form_title TEXT,
  form_type TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_ai_logs_created_at ON public.ai_analysis_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_logs_form_url ON public.ai_analysis_logs(form_url);
CREATE INDEX IF NOT EXISTS idx_ai_logs_model_name ON public.ai_analysis_logs(model_name);
CREATE INDEX IF NOT EXISTS idx_ai_logs_success ON public.ai_analysis_logs(success);
CREATE INDEX IF NOT EXISTS idx_ai_logs_session_id ON public.ai_analysis_logs(session_id);

-- GIN indexes for JSONB queries
CREATE INDEX IF NOT EXISTS idx_ai_logs_form_data_gin ON public.ai_analysis_logs USING GIN (form_data);
CREATE INDEX IF NOT EXISTS idx_ai_logs_suggestions_gin ON public.ai_analysis_logs USING GIN (suggestions);

-- Additional indexes
CREATE INDEX IF NOT EXISTS idx_user_interactions_analysis_log_id ON public.user_interactions(analysis_log_id);
CREATE INDEX IF NOT EXISTS idx_user_interactions_type ON public.user_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_form_metadata_url ON public.form_metadata(form_url);

-- ============================================================================
-- SOLUTION 2: Create RPC functions to access diamond-legal schema (ALTERNATIVE)
-- ============================================================================

-- Only run this section if you want to keep tables in diamond-legal schema
-- and access them via RPC functions

/*
-- Create RPC function to insert AI analysis logs
CREATE OR REPLACE FUNCTION public.log_ai_analysis(
  p_session_id TEXT,
  p_form_url TEXT,
  p_extension_version TEXT,
  p_model_name TEXT,
  p_user_agent TEXT,
  p_form_data JSONB,
  p_filled_fields_count INTEGER,
  p_trigger_reason TEXT,
  p_previous_recommendations JSONB,
  p_dismissed_recommendations JSONB,
  p_full_prompt JSONB,
  p_model_parameters JSONB,
  p_tool_calls JSONB,
  p_iterations INTEGER,
  p_raw_ai_response JSONB,
  p_reasoning_tokens INTEGER,
  p_total_duration_ms INTEGER,
  p_api_response_time_ms INTEGER,
  p_prompt_tokens INTEGER,
  p_completion_tokens INTEGER,
  p_suggestions JSONB,
  p_overall_assessment TEXT,
  p_success BOOLEAN,
  p_errors JSONB
) RETURNS BIGINT AS $$
DECLARE
  new_id BIGINT;
BEGIN
  INSERT INTO "diamond-legal".ai_analysis_logs (
    session_id, form_url, extension_version, model_name, user_agent,
    form_data, filled_fields_count, trigger_reason, previous_recommendations,
    dismissed_recommendations, full_prompt, model_parameters, tool_calls,
    iterations, raw_ai_response, reasoning_tokens, total_duration_ms,
    api_response_time_ms, prompt_tokens, completion_tokens, suggestions,
    overall_assessment, success, errors
  ) VALUES (
    p_session_id, p_form_url, p_extension_version, p_model_name, p_user_agent,
    p_form_data, p_filled_fields_count, p_trigger_reason, p_previous_recommendations,
    p_dismissed_recommendations, p_full_prompt, p_model_parameters, p_tool_calls,
    p_iterations, p_raw_ai_response, p_reasoning_tokens, p_total_duration_ms,
    p_api_response_time_ms, p_prompt_tokens, p_completion_tokens, p_suggestions,
    p_overall_assessment, p_success, p_errors
  ) RETURNING id INTO new_id;
  
  RETURN new_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create RPC function to log user interactions
CREATE OR REPLACE FUNCTION public.log_user_interaction(
  p_analysis_log_id BIGINT,
  p_interaction_type TEXT,
  p_suggestion_id TEXT,
  p_details JSONB
) RETURNS BOOLEAN AS $$
BEGIN
  INSERT INTO "diamond-legal".user_interactions (
    analysis_log_id, interaction_type, suggestion_id, details
  ) VALUES (
    p_analysis_log_id, p_interaction_type, p_suggestion_id, p_details
  );
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create RPC function to query recent logs
CREATE OR REPLACE FUNCTION public.get_recent_ai_logs(p_limit INTEGER DEFAULT 10)
RETURNS TABLE (
  id BIGINT,
  session_id TEXT,
  form_url TEXT,
  model_name TEXT,
  success BOOLEAN,
  created_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    l.id, l.session_id, l.form_url, l.model_name, l.success, l.created_at
  FROM "diamond-legal".ai_analysis_logs l
  ORDER BY l.created_at DESC
  LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.log_ai_analysis TO anon, authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.log_user_interaction TO anon, authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.get_recent_ai_logs TO anon, authenticated, service_role;
*/

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Check that tables were created successfully
SELECT 
  schemaname,
  tablename,
  tableowner
FROM pg_tables 
WHERE tablename IN ('ai_analysis_logs', 'user_interactions', 'form_metadata')
ORDER BY schemaname, tablename;

-- Test insert (you can delete this test record after verification)
INSERT INTO public.ai_analysis_logs (
  session_id,
  form_url,
  model_name,
  form_data,
  filled_fields_count,
  trigger_reason,
  full_prompt,
  total_duration_ms,
  api_response_time_ms,
  suggestions,
  overall_assessment
) VALUES (
  'setup_test',
  'https://setup-test.example.com',
  'setup-test-model',
  '{"test": "setup"}',
  1,
  'setup_verification',
  '[{"role": "user", "content": "setup test"}]',
  1000,
  500,
  '[]',
  'Setup verification test'
);

-- Verify the test insert worked
SELECT id, session_id, form_url, created_at FROM public.ai_analysis_logs WHERE session_id = 'setup_test';