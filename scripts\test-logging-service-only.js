import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://vgjhxvpxooboinitmwrg.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZnamh4dnB4b29ib2luaXRtd3JnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcxNTI3Njc3NSwiZXhwIjoyMDMwODUyNzc1fQ.-hsT_nvM2By1bDV5H03KvXNxEERbuzSK3-E2QMi9cT0';

// Recreate the AILogger class for testing
class AILogger {
  constructor() {
    this.sessionId = `test_session_${Date.now()}`;
    this.extensionVersion = '1.0.0';
    this.supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
  }
  
  async logAIAnalysis(data) {
    try {
      const logEntry = {
        session_id: this.sessionId,
        created_at: new Date().toISOString(),
        form_url: data.formUrl,
        extension_version: this.extensionVersion,
        model_name: data.modelName,
        user_agent: data.userAgent || navigator.userAgent,
        form_data: data.formData,
        filled_fields_count: data.filledFieldsCount,
        trigger_reason: data.triggerReason,
        previous_recommendations: data.previousRecommendations,
        dismissed_recommendations: data.dismissedRecommendations,
        full_prompt: data.fullPrompt,
        model_parameters: data.modelParameters,
        tool_calls: data.toolCalls,
        iterations: data.iterations,
        raw_ai_response: data.rawAIResponse,
        reasoning_tokens: data.reasoningTokens,
        total_duration_ms: data.totalDuration,
        api_response_time_ms: data.apiResponseTime,
        prompt_tokens: data.promptTokens,
        completion_tokens: data.completionTokens,
        suggestions: data.suggestions,
        overall_assessment: data.overallAssessment,
        success: data.success,
        errors: data.errors || []
      };
      
      console.log('🔄 Logging AI analysis to diamond-legal schema...');
      
      const { data: result, error } = await this.supabase
        .schema('diamond-legal')
        .from('ai_analysis_logs')
        .insert(logEntry)
        .select('id')
        .single();
      
      if (error) {
        console.error('❌ Failed to log AI analysis:', error);
        return null;
      }
      
      console.log('✅ AI analysis logged successfully:', result.id);
      return result.id;
    } catch (error) {
      console.error('❌ Error logging AI analysis:', error);
      return null;
    }
  }
  
  async logUserInteraction(data) {
    try {
      const { error } = await this.supabase
        .schema('diamond-legal')
        .from('user_interactions')
        .insert({
          analysis_log_id: data.analysisLogId,
          interaction_type: data.interactionType,
          suggestion_id: data.suggestionId,
          details: data.details || {}
        });
      
      if (error) {
        console.error('❌ Failed to log user interaction:', error);
        return false;
      }
      
      console.log('✅ User interaction logged:', data.interactionType);
      return true;
    } catch (error) {
      console.error('❌ Error logging user interaction:', error);
      return false;
    }
  }
}

async function testLoggingService() {
    console.log('🧪 Testing AI Logging Service Integration\n');
    
    const logger = new AILogger();
    
    // Test AI analysis logging
    console.log('1️⃣ Testing AI analysis logging...');
    
    const testData = {
        formUrl: 'https://app.lawmatics.com/forms/test-logging',
        modelName: 'google/gemini-2.5-flash',
        formData: {
            'client_name': {
                field_name: 'Client Name',
                field_value: 'John Doe Test',
                required: true,
                field_id: 'client_name'
            },
            'email': {
                field_name: 'Email Address',
                field_value: '<EMAIL>',
                required: true,
                field_id: 'email'
            }
        },
        filledFieldsCount: 2,
        triggerReason: 'logging_service_integration_test',
        previousRecommendations: [],
        dismissedRecommendations: [],
        fullPrompt: [
            {
                role: 'system',
                content: 'You are an AI assistant helping with legal form analysis...'
            },
            {
                role: 'user', 
                content: 'Current intake form data: {...}'
            }
        ],
        modelParameters: { 
            temperature: 0.1,
            reasoning: { max_tokens: 1000, exclude: false }
        },
        toolCalls: [
            {
                name: 'get_current_datetime',
                arguments: {},
                response: 'Current date and time: 06/26/2025 16:54:00',
                executionTime: 5
            },
            {
                name: 'final_assessment',
                arguments: {
                    suggestions: [
                        {
                            id: 'test-suggestion-1',
                            field_id: 'phone_number',
                            suggestion: 'Please provide a valid phone number for contact purposes',
                            priority: 'high',
                            severity: 'medium',
                            type: 'missing_info'
                        }
                    ],
                    overall_assessment: 'Form is partially complete. Missing phone number for client contact.'
                },
                response: 'success',
                executionTime: 15
            }
        ],
        iterations: 2,
        rawAIResponse: {
            id: 'chatcmpl-test123',
            choices: [{
                message: {
                    role: 'assistant',
                    content: 'Analysis complete.',
                    tool_calls: []
                }
            }],
            usage: {
                prompt_tokens: 450,
                completion_tokens: 125,
                total_tokens: 575
            }
        },
        reasoningTokens: 75,
        totalDuration: 2850,
        apiResponseTime: 1200,
        promptTokens: 450,
        completionTokens: 125,
        suggestions: [
            {
                id: 'test-suggestion-1',
                field_id: 'phone_number',
                suggestion: 'Please provide a valid phone number for contact purposes',
                priority: 'high',
                severity: 'medium',
                type: 'missing_info'
            }
        ],
        overallAssessment: 'Form is partially complete. Missing phone number for client contact.',
        success: true
    };
    
    const logId = await logger.logAIAnalysis(testData);
    
    if (!logId) {
        console.log('❌ AI analysis logging failed');
        return false;
    }
    
    // Test user interaction logging
    console.log('\n2️⃣ Testing user interaction logging...');
    
    const interactionSuccess = await logger.logUserInteraction({
        analysisLogId: logId,
        interactionType: 'dismissed',
        suggestionId: 'test-suggestion-1',
        details: { 
            reason: 'integration_test',
            dismissed_at: new Date().toISOString()
        }
    });
    
    if (!interactionSuccess) {
        console.log('❌ User interaction logging failed');
        return false;
    }
    
    // Query back the data to verify
    console.log('\n3️⃣ Verifying logged data...');
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    
    const { data: logData, error: logError } = await supabase
        .schema('diamond-legal')
        .from('ai_analysis_logs')
        .select('id, session_id, form_url, model_name, success, suggestions, created_at')
        .eq('id', logId)
        .single();
    
    if (logError) {
        console.log('❌ Failed to verify logged data:', logError.message);
        return false;
    }
    
    console.log('✅ Verified AI analysis log:');
    console.log(`   📊 ID: ${logData.id}`);
    console.log(`   🔗 Session: ${logData.session_id}`);
    console.log(`   🌐 Form URL: ${logData.form_url}`);
    console.log(`   🤖 Model: ${logData.model_name}`);
    console.log(`   ✅ Success: ${logData.success}`);
    console.log(`   💡 Suggestions: ${logData.suggestions.length}`);
    console.log(`   📅 Created: ${logData.created_at}`);
    
    const { data: interactionData, error: interactionError } = await supabase
        .schema('diamond-legal')
        .from('user_interactions')
        .select('id, interaction_type, suggestion_id, details, created_at')
        .eq('analysis_log_id', logId);
    
    if (interactionError) {
        console.log('❌ Failed to verify interaction data:', interactionError.message);
        return false;
    }
    
    console.log('\n✅ Verified user interactions:');
    interactionData.forEach(interaction => {
        console.log(`   📊 ID: ${interaction.id}`);
        console.log(`   🎯 Type: ${interaction.interaction_type}`);
        console.log(`   💡 Suggestion: ${interaction.suggestion_id}`);
        console.log(`   📅 Created: ${interaction.created_at}`);
    });
    
    return true;
}

async function runTest() {
    console.log('🚀 Starting comprehensive logging service test...\n');
    
    const success = await testLoggingService();
    
    console.log('\n📋 TEST SUMMARY');
    console.log('===============');
    
    if (success) {
        console.log('🎉 All logging service tests passed!');
        console.log('💡 Your AI performance logging system is fully operational.');
        console.log('\n🔍 What gets logged:');
        console.log('   ✅ Complete form state and AI prompts');
        console.log('   ✅ Tool calls and execution timing');
        console.log('   ✅ AI responses and reasoning tokens');
        console.log('   ✅ Performance metrics and token usage');
        console.log('   ✅ Generated suggestions with priorities');
        console.log('   ✅ User interactions with suggestions');
        console.log('\n📊 Ready for production use!');
    } else {
        console.log('❌ Logging service tests failed.');
        console.log('⚠️  Check errors above for troubleshooting.');
    }
    
    return success;
}

// Run the test
runTest().catch(console.error);