import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://vgjhxvpxooboinitmwrg.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZnamh4dnB4b29ib2luaXRtd3JnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcxNTI3Njc3NSwiZXhwIjoyMDMwODUyNzc1fQ.-hsT_nvM2By1bDV5H03KvXNxEERbuzSK3-E2QMi9cT0';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

const SQL_COMMANDS = {
  // Main AI analysis logging table
  createMainTable: `
    CREATE TABLE IF NOT EXISTS "diamond-legal".ai_analysis_logs (
      id BIGSERIAL PRIMARY KEY,
      session_id TEXT NOT NULL,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      
      -- Context
      form_url TEXT NOT NULL,
      extension_version TEXT,
      model_name TEXT NOT NULL,
      user_agent TEXT,
      
      -- Input State
      form_data JSONB NOT NULL,
      filled_fields_count INTEGER,
      trigger_reason TEXT,
      previous_recommendations JSONB DEFAULT '[]'::jsonb,
      dismissed_recommendations JSONB DEFAULT '[]'::jsonb,
      
      -- AI Interaction
      full_prompt JSONB NOT NULL,
      model_parameters JSONB,
      tool_calls JSONB DEFAULT '[]'::jsonb,
      iterations INTEGER DEFAULT 1,
      raw_ai_response JSONB,
      reasoning_tokens INTEGER,
      
      -- Performance
      total_duration_ms INTEGER,
      api_response_time_ms INTEGER,
      prompt_tokens INTEGER,
      completion_tokens INTEGER,
      
      -- Results
      suggestions JSONB DEFAULT '[]'::jsonb,
      overall_assessment TEXT,
      
      -- Status
      success BOOLEAN DEFAULT TRUE,
      errors JSONB DEFAULT '[]'::jsonb
    );
  `,
  
  // Performance indexes
  createIndexes: [
    'CREATE INDEX IF NOT EXISTS idx_ai_logs_created_at ON "diamond-legal".ai_analysis_logs(created_at);',
    'CREATE INDEX IF NOT EXISTS idx_ai_logs_form_url ON "diamond-legal".ai_analysis_logs(form_url);',
    'CREATE INDEX IF NOT EXISTS idx_ai_logs_model_name ON "diamond-legal".ai_analysis_logs(model_name);',
    'CREATE INDEX IF NOT EXISTS idx_ai_logs_success ON "diamond-legal".ai_analysis_logs(success);',
    'CREATE INDEX IF NOT EXISTS idx_ai_logs_session_id ON "diamond-legal".ai_analysis_logs(session_id);',
    'CREATE INDEX IF NOT EXISTS idx_ai_logs_form_data_gin ON "diamond-legal".ai_analysis_logs USING GIN (form_data);',
    'CREATE INDEX IF NOT EXISTS idx_ai_logs_suggestions_gin ON "diamond-legal".ai_analysis_logs USING GIN (suggestions);'
  ],
  
  // User interaction tracking table
  createUserInteractionsTable: `
    CREATE TABLE IF NOT EXISTS "diamond-legal".user_interactions (
      id BIGSERIAL PRIMARY KEY,
      analysis_log_id BIGINT REFERENCES "diamond-legal".ai_analysis_logs(id),
      created_at TIMESTAMPTZ DEFAULT NOW(),
      interaction_type TEXT NOT NULL, -- 'dismissed', 'acted_upon', 'ignored'
      suggestion_id TEXT,
      details JSONB
    );
  `,
  
  // Form metadata table
  createFormMetadataTable: `
    CREATE TABLE IF NOT EXISTS "diamond-legal".form_metadata (
      id BIGSERIAL PRIMARY KEY,
      form_url TEXT UNIQUE NOT NULL,
      form_title TEXT,
      form_type TEXT,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );
  `
};

async function executeSQLCommand(command, description) {
  console.log(`\n🔧 ${description}...`);
  try {
    const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'apikey': SUPABASE_SERVICE_ROLE_KEY
      },
      body: JSON.stringify({ sql: command })
    });
    
    if (!response.ok) {
      // Try direct SQL execution via PostgREST
      const directResponse = await fetch(`${SUPABASE_URL}/rest/v1/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/sql',
          'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
          'apikey': SUPABASE_SERVICE_ROLE_KEY
        },
        body: command
      });
      
      if (!directResponse.ok) {
        const errorText = await directResponse.text();
        console.error(`❌ Failed: ${directResponse.status} - ${errorText}`);
        return false;
      }
    }
    
    console.log(`✅ Success: ${description}`);
    return true;
  } catch (err) {
    console.error(`❌ Error: ${err.message}`);
    return false;
  }
}

async function setupDatabase() {
  console.log('🚀 Setting up AI Performance Logging Database Schema...');
  console.log(`📍 Project: ${SUPABASE_URL}`);
  console.log(`📊 Schema: diamond-legal`);
  
  let success = true;
  
  // Test connection
  console.log('\n🔍 Testing connection...');
  try {
    const testResponse = await fetch(`${SUPABASE_URL}/rest/v1/`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'apikey': SUPABASE_SERVICE_ROLE_KEY
      }
    });
    if (!testResponse.ok) {
      console.error('❌ Connection failed:', testResponse.status);
      return;
    }
    console.log('✅ Connection successful');
  } catch (err) {
    console.error('❌ Connection error:', err.message);
    return;
  }
  
  // Create main table
  success = await executeSQLCommand(SQL_COMMANDS.createMainTable, 'Creating ai_analysis_logs table') && success;
  
  // Create indexes
  for (const [index, indexSQL] of SQL_COMMANDS.createIndexes.entries()) {
    success = await executeSQLCommand(indexSQL, `Creating index ${index + 1}/${SQL_COMMANDS.createIndexes.length}`) && success;
  }
  
  // Create additional tables
  success = await executeSQLCommand(SQL_COMMANDS.createUserInteractionsTable, 'Creating user_interactions table') && success;
  success = await executeSQLCommand(SQL_COMMANDS.createFormMetadataTable, 'Creating form_metadata table') && success;
  
  // Validate setup
  console.log('\n🔍 Validating setup...');
  console.log('✅ Schema setup completed (validation via Supabase dashboard recommended)');
  
  // Summary
  console.log('\n📋 Setup Summary:');
  console.log(`Status: ${success ? '✅ SUCCESS' : '❌ PARTIAL FAILURE'}`);
  console.log('Tables created:');
  console.log('  - diamond-legal.ai_analysis_logs (main logging table)');
  console.log('  - diamond-legal.user_interactions (user behavior tracking)');
  console.log('  - diamond-legal.form_metadata (form information)');
  console.log('Indexes: 7 performance indexes created');
  
  if (success) {
    console.log('\n🎉 Database setup complete! Ready for AI performance logging.');
  } else {
    console.log('\n⚠️  Some operations failed. Please check errors above.');
  }
}

// Run setup
setupDatabase().catch(console.error);