{"name": "prime-legal-assistant", "description": "Prime Legal Assistant - AI-powered legal form analysis and recommendations", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "tsc --noEmit", "postinstall": "wxt prepare"}, "dependencies": {"@supabase/supabase-js": "^2.50.2", "dayjs": "^1.11.13", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "@wxt-dev/module-react": "^1.1.3", "openai": "^5.1.1", "typescript": "^5.8.3", "vitest": "^3.2.2", "wxt": "^0.20.6"}}