import { DismissedRecommendation, FormSuggestion } from './ai';

/**
 * Session storage utilities for managing dismissed recommendations per URL
 */

const getDismissedStorageKey = (url: string): string => {
    return `dismissed_recommendations_${url}`;
};

/**
 * Get dismissed recommendations for the current URL
 */
export async function getDismissedRecommendations(url: string = window.location.href): Promise<DismissedRecommendation[]> {
    try {
        const response = await browser.runtime.sendMessage({
            type: 'GET_DISMISSED_RECOMMENDATIONS',
            url: url
        });
        return response?.dismissedRecommendations || [];
    } catch (error) {
        console.error('Error getting dismissed recommendations:', error);
        return [];
    }
}

/**
 * Add a recommendation to the dismissed list for the current URL
 */
export async function addDismissedRecommendation(
    suggestionId: string, 
    fieldId: string, 
    suggestionText: string,
    url: string = window.location.href
): Promise<void> {
    try {
        await browser.runtime.sendMessage({
            type: 'ADD_DISMISSED_RECOMMENDATION',
            url: url,
            suggestion: {
                id: suggestionId,
                field_id: fieldId,
                suggestion_text: suggestionText,
                timestamp: Date.now(),
                dismissed_since_last_refresh: true
            }
        });
    } catch (error) {
        console.error('Error adding dismissed recommendation:', error);
    }
}

/**
 * Clear all dismissed recommendations for the current URL
 */
export async function clearDismissedRecommendations(url: string = window.location.href): Promise<void> {
    try {
        await browser.runtime.sendMessage({
            type: 'CLEAR_DISMISSED_RECOMMENDATIONS',
            url: url
        });
    } catch (error) {
        console.error('Error clearing dismissed recommendations:', error);
    }
}

/**
 * Get previous recommendations from local storage
 */
export async function getPreviousRecommendations(): Promise<FormSuggestion[]> {
    try {
        const result = await browser.storage.local.get('ai_suggestions');
        const feedback = result.ai_suggestions;
        return feedback?.suggestions || [];
    } catch (error) {
        console.error('Error getting previous recommendations:', error);
        return [];
    }
}

/**
 * Remove a specific recommendation from dismissed list (for un-dismissing)
 */
export async function removeDismissedRecommendation(
    suggestionId: string,
    url: string = window.location.href
): Promise<void> {
    try {
        const key = getDismissedStorageKey(url);
        const currentDismissed = await getDismissedRecommendations(url);
        const filtered = currentDismissed.filter(item => item.id !== suggestionId);
        await browser.storage.session.set({ [key]: filtered });
    } catch (error) {
        console.error('Error removing dismissed recommendation:', error);
    }
}

/**
 * Get only dismissed recommendations that are active since last refresh (for UI filtering)
 */
export async function getActiveDismissedRecommendations(url: string = window.location.href): Promise<DismissedRecommendation[]> {
    const allDismissed = await getDismissedRecommendations(url);
    return allDismissed.filter(item => item.dismissed_since_last_refresh);
}

/**
 * Mark all dismissed recommendations as no longer active since refresh (allows them to show again)
 */
export async function markAllDismissalsAsRefreshed(url: string = window.location.href): Promise<void> {
    try {
        await browser.runtime.sendMessage({
            type: 'MARK_DISMISSALS_AS_REFRESHED',
            url: url
        });
    } catch (error) {
        console.error('Error marking dismissals as refreshed:', error);
    }
}