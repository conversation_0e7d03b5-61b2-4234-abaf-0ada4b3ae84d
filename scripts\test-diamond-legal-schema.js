import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'https://vgjhxvpxooboinitmwrg.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZnamh4dnB4b29ib2luaXRtd3JnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcxNTI3Njc3NSwiZXhwIjoyMDMwODUyNzc1fQ.-hsT_nvM2By1bDV5H03KvXNxEERbuzSK3-E2QMi9cT0';

console.log('🧪 Testing diamond-legal schema access...\n');

async function testSchemaAccess() {
    console.log('1️⃣ Testing schema access...');
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    const tables = ['ai_analysis_logs', 'user_interactions', 'form_metadata'];
    
    for (const table of tables) {
        try {
            console.log(`\n🔍 Testing diamond-legal.${table}...`);
            
            const { data, error } = await supabase
                .schema('diamond-legal')
                .from(table)
                .select('*')
                .limit(1);
            
            if (error) {
                console.log(`   ❌ Error: ${error.message}`);
                console.log(`   📋 Error details:`, error);
            } else {
                console.log(`   ✅ Success! Found ${data.length} rows`);
                if (data.length > 0) {
                    console.log(`   📄 Sample columns:`, Object.keys(data[0]));
                }
            }
        } catch (err) {
            console.log(`   ❌ Exception: ${err.message}`);
        }
    }
}

async function testInsert() {
    console.log('\n2️⃣ Testing insert into diamond-legal.ai_analysis_logs...');
    
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    
    const testData = {
        session_id: `test_schema_${Date.now()}`,
        form_url: 'https://test-schema.example.com',
        extension_version: '1.0.0',
        model_name: 'test-schema-model',
        user_agent: 'Test/1.0',
        form_data: { test_schema: 'value' },
        filled_fields_count: 1,
        trigger_reason: 'schema_test',
        previous_recommendations: [],
        dismissed_recommendations: [],
        full_prompt: [{ role: 'user', content: 'schema test' }],
        model_parameters: { temperature: 0.1 },
        tool_calls: [],
        iterations: 1,
        raw_ai_response: { test: 'schema_response' },
        total_duration_ms: 1500,
        api_response_time_ms: 750,
        prompt_tokens: 120,
        completion_tokens: 60,
        suggestions: [],
        overall_assessment: 'Schema test successful',
        success: true,
        errors: []
    };
    
    try {
        const { data, error } = await supabase
            .schema('diamond-legal')
            .from('ai_analysis_logs')
            .insert(testData)
            .select('id, session_id, created_at');
        
        if (error) {
            console.log('❌ Insert failed:', error.message);
            console.log('📋 Error details:', error);
            return false;
        }
        
        console.log('✅ Insert successful!');
        console.log('📊 Inserted record:', data[0]);
        return data[0].id;
    } catch (err) {
        console.log('❌ Insert exception:', err.message);
        return false;
    }
}

async function testLoggingService() {
    console.log('\n3️⃣ Testing AI logging service...');
    
    try {
        // Import our logging service
        const { aiLogger } = await import('../utils/aiLogger.js');
        
        const testData = {
            formUrl: 'https://service-test.example.com',
            modelName: 'service-test-model',
            formData: { service_test: 'logging_service' },
            filledFieldsCount: 3,
            triggerReason: 'logging_service_test',
            previousRecommendations: [],
            dismissedRecommendations: [],
            fullPrompt: [{ role: 'user', content: 'logging service test' }],
            modelParameters: { temperature: 0.15 },
            toolCalls: [],
            iterations: 1,
            rawAIResponse: { service_test: 'response' },
            totalDuration: 2500,
            apiResponseTime: 1200,
            promptTokens: 180,
            completionTokens: 90,
            suggestions: [
                {
                    id: 'test-suggestion-1',
                    field_id: 'test_field',
                    suggestion: 'This is a test suggestion',
                    priority: 'medium',
                    severity: 'low',
                    type: 'missing_info'
                }
            ],
            overallAssessment: 'Logging service integration test',
            success: true
        };
        
        console.log('🔄 Calling aiLogger.logAIAnalysis...');
        const logId = await aiLogger.logAIAnalysis(testData);
        
        if (logId) {
            console.log('✅ Logging service test successful!');
            console.log('📊 Log ID:', logId);
            
            // Test user interaction logging
            console.log('\n🔄 Testing user interaction logging...');
            const interactionSuccess = await aiLogger.logUserInteraction({
                analysisLogId: logId,
                interactionType: 'dismissed',
                suggestionId: 'test-suggestion-1',
                details: { reason: 'service_test' }
            });
            
            if (interactionSuccess) {
                console.log('✅ User interaction logging successful!');
            } else {
                console.log('❌ User interaction logging failed');
            }
            
            return true;
        } else {
            console.log('❌ Logging service test failed');
            return false;
        }
    } catch (err) {
        console.log('❌ Logging service error:', err.message);
        return false;
    }
}

async function runTests() {
    console.log('🚀 Running diamond-legal schema tests...\n');
    
    const results = {
        schemaAccess: false,
        insert: false,
        loggingService: false
    };
    
    await testSchemaAccess();
    results.insert = await testInsert();
    
    if (results.insert) {
        results.loggingService = await testLoggingService();
    }
    
    // Summary
    console.log('\n📋 TEST SUMMARY');
    console.log('===============');
    console.log(`Schema Access: ✅ COMPLETED`);
    console.log(`Direct Insert: ${results.insert ? '✅ SUCCESS' : '❌ FAILED'}`);
    console.log(`Logging Service: ${results.loggingService ? '✅ SUCCESS' : '❌ FAILED'}`);
    
    if (results.insert && results.loggingService) {
        console.log('\n🎉 All tests passed! Schema access is working correctly.');
        console.log('💡 Your AI logging system is ready to use.');
    } else {
        console.log('\n⚠️  Some tests failed. Check the errors above.');
    }
    
    return results;
}

// Run tests
runTests().catch(console.error);