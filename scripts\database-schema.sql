-- AI Performance Logging Database Schema
-- Execute this in your Supabase SQL Editor

-- Main AI analysis logging table
CREATE TABLE IF NOT EXISTS ai_analysis_logs (
  id BIGSERIAL PRIMARY KEY,
  session_id TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Context
  form_url TEXT NOT NULL,
  extension_version TEXT,
  model_name TEXT NOT NULL,
  user_agent TEXT,
  
  -- Input State
  form_data JSONB NOT NULL,
  filled_fields_count INTEGER,
  trigger_reason TEXT,
  previous_recommendations JSONB DEFAULT '[]'::jsonb,
  dismissed_recommendations JSONB DEFAULT '[]'::jsonb,
  
  -- AI Interaction
  full_prompt JSONB NOT NULL,
  model_parameters JSONB,
  tool_calls JSONB DEFAULT '[]'::jsonb,
  iterations INTEGER DEFAULT 1,
  raw_ai_response JSONB,
  reasoning_tokens INTEGER,
  
  -- Performance
  total_duration_ms INTEGER,
  api_response_time_ms INTEGER,
  prompt_tokens INTEGER,
  completion_tokens INTEGER,
  
  -- Results
  suggestions JSONB DEFAULT '[]'::jsonb,
  overall_assessment TEXT,
  
  -- Status
  success BOOLEAN DEFAULT TRUE,
  errors JSONB DEFAULT '[]'::jsonb
);

-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_ai_logs_created_at ON ai_analysis_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_logs_form_url ON ai_analysis_logs(form_url);
CREATE INDEX IF NOT EXISTS idx_ai_logs_model_name ON ai_analysis_logs(model_name);
CREATE INDEX IF NOT EXISTS idx_ai_logs_success ON ai_analysis_logs(success);
CREATE INDEX IF NOT EXISTS idx_ai_logs_session_id ON ai_analysis_logs(session_id);

-- GIN indexes for JSONB queries
CREATE INDEX IF NOT EXISTS idx_ai_logs_form_data_gin ON ai_analysis_logs USING GIN (form_data);
CREATE INDEX IF NOT EXISTS idx_ai_logs_suggestions_gin ON ai_analysis_logs USING GIN (suggestions);

-- User interaction tracking table
CREATE TABLE IF NOT EXISTS user_interactions (
  id BIGSERIAL PRIMARY KEY,
  analysis_log_id BIGINT REFERENCES ai_analysis_logs(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  interaction_type TEXT NOT NULL, -- 'dismissed', 'acted_upon', 'ignored'
  suggestion_id TEXT,
  details JSONB
);

-- Form metadata table
CREATE TABLE IF NOT EXISTS form_metadata (
  id BIGSERIAL PRIMARY KEY,
  form_url TEXT UNIQUE NOT NULL,
  form_title TEXT,
  form_type TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for user_interactions
CREATE INDEX IF NOT EXISTS idx_user_interactions_analysis_log_id ON user_interactions(analysis_log_id);
CREATE INDEX IF NOT EXISTS idx_user_interactions_type ON user_interactions(interaction_type);

-- Add indexes for form_metadata
CREATE INDEX IF NOT EXISTS idx_form_metadata_url ON form_metadata(form_url);

-- Verify tables were created
SELECT table_name, table_schema 
FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name LIKE '%ai_analysis%' OR table_name LIKE '%user_interactions%' OR table_name LIKE '%form_metadata%'
ORDER BY table_name;